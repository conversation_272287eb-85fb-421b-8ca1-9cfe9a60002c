<script setup>
import { ref } from 'vue';

/*
 * JotForm Integration Setup Instructions:
 *
 * 1. Create a new form at https://www.jotform.com/
 * 2. Add the following fields to your JotForm:
 *    - Name (Text field)
 *    - Phone (Phone field)
 *    - Email (Email field)
 *    - Service Type (Dropdown with options: Office Cleaning, Retail Spaces, Medical Facilities, Educational Institutions, Hospitality, Industrial Cleaning)
 *    - Square Footage (Number field)
 *
 * 3. Get your form ID from the JotForm URL (e.g., if URL is https://form.jotform.com/123456789, then ID is 123456789)
 * 4. Replace 'YOUR_JOTFORM_ID' below with your actual form ID
 *
 * 5. Get the field names from your JotForm:
 *    - Go to your form settings
 *    - Click on "Form Source Code"
 *    - Look for input names like "q3_name", "q4_phone", etc.
 *    - Replace the field names in the formData.append() calls below
 *
 * 6. Set up email notifications in your JotForm settings to receive submissions
 */

const quickQuoteData = ref({
  name: '',
  phone: '',
  email: '',
  serviceType: 'Office Cleaning',
  squareFootage: ''
});

const isSubmitting = ref(false);
const isSubmitted = ref(false);

// Service types
const serviceTypes = [
  'Office Cleaning',
  'Retail Spaces', 
  'Medical Facilities',
  'Educational Institutions',
  'Hospitality',
  'Industrial Cleaning'
];

// JotForm configuration - Replace with your actual JotForm ID
const JOTFORM_ID = 'YOUR_JOTFORM_ID'; // Replace this with your actual JotForm ID
const JOTFORM_SUBMIT_URL = `https://submit.jotform.com/submit/${JOTFORM_ID}`;

// Submit quick quote to JotForm
const submitQuickQuote = async () => {
  if (!quickQuoteData.value.name || !quickQuoteData.value.phone || !quickQuoteData.value.email) {
    return;
  }

  isSubmitting.value = true;

  try {
    // Prepare form data for JotForm submission
    const formData = new FormData();

    // Map our form fields to JotForm field IDs
    // Note: You'll need to replace these field IDs with your actual JotForm field IDs
    formData.append('q3_name', quickQuoteData.value.name); // Replace 'q3_name' with actual field name
    formData.append('q4_phone', quickQuoteData.value.phone); // Replace 'q4_phone' with actual field name
    formData.append('q5_email', quickQuoteData.value.email); // Replace 'q5_email' with actual field name
    formData.append('q6_serviceType', quickQuoteData.value.serviceType); // Replace 'q6_serviceType' with actual field name
    formData.append('q7_squareFootage', quickQuoteData.value.squareFootage); // Replace 'q7_squareFootage' with actual field name

    // Submit to JotForm
    const response = await fetch(JOTFORM_SUBMIT_URL, {
      method: 'POST',
      body: formData,
      mode: 'no-cors' // JotForm requires no-cors mode
    });

    // Since we're using no-cors, we can't read the response
    // But if we get here without an error, the submission likely succeeded
    isSubmitting.value = false;
    isSubmitted.value = true;

    // Reset form
    quickQuoteData.value = {
      name: '',
      phone: '',
      email: '',
      serviceType: 'Office Cleaning',
      squareFootage: ''
    };

    // Hide success message after 5 seconds
    setTimeout(() => {
      isSubmitted.value = false;
    }, 5000);

  } catch (error) {
    console.error('Error submitting form:', error);
    isSubmitting.value = false;

    // Fallback: Open JotForm in new tab with pre-filled data
    const jotformUrl = `https://form.jotform.com/${JOTFORM_ID}?name=${encodeURIComponent(quickQuoteData.value.name)}&phone=${encodeURIComponent(quickQuoteData.value.phone)}&email=${encodeURIComponent(quickQuoteData.value.email)}`;

    if (confirm('There was an issue submitting your request. Would you like to open our contact form in a new tab?')) {
      window.open(jotformUrl, '_blank');
    }
  }
};
</script>

<template>
  <div class="quick-quote-form">
    <div class="form-header">
      <h3 class="form-title">GET A INSTANT ROUGH QUOTE!</h3>
    </div>

    <div v-if="isSubmitted" class="success-message fade-in-up">
      <div class="success-icon pulse-animation">✓</div>
      <p>Quote request sent! We'll contact you within 24 hours.</p>
    </div>

    <form v-else @submit.prevent="submitQuickQuote" class="quote-form">
      <div class="form-fields">
        <input
          type="text"
          v-model="quickQuoteData.name"
          placeholder="Your Name *"
          required
          class="form-input"
        >
        <input
          type="tel"
          v-model="quickQuoteData.phone"
          placeholder="Phone Number *"
          required
          class="form-input"
        >
        <input
          type="email"
          v-model="quickQuoteData.email"
          placeholder="Email Address *"
          required
          class="form-input"
        >
        <input
          type="number"
          v-model="quickQuoteData.squareFootage"
          placeholder="Square Footage"
          class="form-input"
        >
        <select v-model="quickQuoteData.serviceType" class="form-select">
          <option v-for="service in serviceTypes" :key="service" :value="service">
            {{ service }}
          </option>
        </select>
      </div>

      <button
        type="submit"
        class="submit-btn"
        :disabled="isSubmitting"
      >
        <span v-if="isSubmitting">Sending...</span>
        <span v-else>GET QUOTE</span>
      </button>
    </form>
  </div>
</template>

<style scoped>
.quick-quote-form {
  background: rgba(255, 255, 255, 0.95);
  border: 2px solid var(--color-teal);
  border-radius: 12px;
  padding: 2.5rem;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.form-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.form-title {
  font-family: 'Bricolage Grotesque', 'Arial', sans-serif !important;
  font-size: clamp(1.8rem, 3vw, 2.5rem);
  font-weight: 800 !important;
  color: var(--color-teal);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0;
  text-align: center;
}

.quote-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-fields {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-input,
.form-select {
  padding: 1rem;
  border: 2px solid var(--color-teal-light);
  border-radius: 8px;
  font-size: 1rem;
  font-family: 'Bricolage Grotesque', 'Arial', sans-serif !important;
  font-weight: 500 !important;
  transition: all 0.3s ease;
  background: #FFFFFF;
  color: var(--color-black);
  width: 100%;
  box-sizing: border-box;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: var(--color-teal);
  box-shadow: 0 0 10px rgba(8, 145, 178, 0.3);
}

.form-input::placeholder {
  font-family: 'Bricolage Grotesque', 'Arial', sans-serif !important;
  font-weight: 400 !important;
  color: #666;
}

.submit-btn {
  background: var(--color-teal);
  color: #FFFFFF;
  border: 2px solid var(--color-teal);
  padding: 1rem 2rem;
  border-radius: 8px;
  font-family: 'Bricolage Grotesque', 'Arial', sans-serif !important;
  font-weight: 700 !important;
  font-size: 1.1rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  margin-top: 1rem;
}

.submit-btn:hover:not(:disabled) {
  background: var(--color-teal-dark);
  color: #FFFFFF;
  border-color: var(--color-teal-dark);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(14, 116, 144, 0.4);
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.success-message {
  text-align: center;
  padding: 1rem;
}

.success-icon {
  background: var(--color-teal);
  color: #FFFFFF;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 0.75rem;
  font-size: 1.2rem;
  font-weight: bold;
}

.success-message p {
  color: var(--color-teal);
  font-weight: 500;
  margin: 0;
}

@media (max-width: 768px) {
  .quick-quote-form {
    padding: 1.5rem;
  }

  .form-title {
    font-size: 2rem;
    font-weight: 800 !important;
    letter-spacing: 0.1em;
  }

  .form-input,
  .form-select {
    font-size: 1rem;
    padding: 0.875rem;
    font-weight: 500 !important;
  }

  .submit-btn {
    font-size: 1.1rem;
    padding: 0.875rem 1.5rem;
  }
}
</style>
