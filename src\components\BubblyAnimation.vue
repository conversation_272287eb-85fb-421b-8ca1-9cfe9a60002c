<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import * as THREE from 'three';
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer.js';
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass.js';
import { UnrealBloomPass } from 'three/examples/jsm/postprocessing/UnrealBloomPass.js';

const canvasRef = ref(null);
let scene, camera, renderer;
let waterMesh, waterMaterial, waterGeometry;
let bubbleSystem, bubbleGeometry, bubbleMaterial;
let popSprites = [];
let raycaster, mouse;
let mouseX = 0, mouseY = 0;
let windowHalfX = window.innerWidth / 2;
let windowHalfY = window.innerHeight / 2;
let animationFrameId = null;
let bubblePositions, bubbleVelocities, bubbleScales, bubbleRotations, bubbleStates;
let popSound, bubbleSound;
let clock = new THREE.Clock();
let composer; // For post-processing effects

// Animation properties
const props = defineProps({
  color: {
    type: String,
    default: '#FCCB01' // Golden yellow
  },
  particleCount: {
    type: Number,
    default: 100
  },
  particleSize: {
    type: Number,
    default: 8
  },
  bloomStrength: {
    type: Number,
    default: 0.8
  },
  bloomRadius: {
    type: Number,
    default: 0.5
  },
  bloomThreshold: {
    type: Number,
    default: 0.2
  }
});

// Create a stylized Arcane-like bubble texture
const createArcaneBubbleTexture = () => {
  const canvas = document.createElement('canvas');
  canvas.width = 256;
  canvas.height = 256;
  const ctx = canvas.getContext('2d');
  
  // Clear canvas
  ctx.clearRect(0, 0, 256, 256);
  
  // Create base bubble shape
  const gradient = ctx.createRadialGradient(128, 128, 0, 128, 128, 128);
  gradient.addColorStop(0, 'rgba(180, 230, 255, 0.2)');
  gradient.addColorStop(0.4, 'rgba(120, 200, 255, 0.3)');
  gradient.addColorStop(0.7, 'rgba(80, 170, 255, 0.2)');
  gradient.addColorStop(0.9, 'rgba(40, 120, 220, 0.1)');
  gradient.addColorStop(1, 'rgba(20, 80, 180, 0)');
  
  ctx.fillStyle = gradient;
  ctx.beginPath();
  ctx.arc(128, 128, 120, 0, Math.PI * 2);
  ctx.fill();
  
  // Add cel-shaded highlight (hard edge)
  const highlightGradient = ctx.createRadialGradient(90, 90, 0, 90, 90, 80);
  highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.9)');
  highlightGradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.5)');
  highlightGradient.addColorStop(0.7, 'rgba(255, 255, 255, 0.1)');
  highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
  
  ctx.fillStyle = highlightGradient;
  ctx.beginPath();
  ctx.arc(90, 90, 60, 0, Math.PI * 2);
  ctx.fill();
  
  // Add painterly texture effect
  ctx.globalCompositeOperation = 'overlay';
  for (let i = 0; i < 20; i++) {
    const x = Math.random() * 256;
    const y = Math.random() * 256;
    const radius = Math.random() * 30 + 5;
    const opacity = Math.random() * 0.05;
    
    ctx.fillStyle = `rgba(255, 255, 255, ${opacity})`;
    ctx.beginPath();
    ctx.arc(x, y, radius, 0, Math.PI * 2);
    ctx.fill();
  }
  
  // Add hard edge to bubble
  ctx.globalCompositeOperation = 'source-over';
  ctx.strokeStyle = 'rgba(120, 200, 255, 0.6)';
  ctx.lineWidth = 4;
  ctx.beginPath();
  ctx.arc(128, 128, 120, 0, Math.PI * 2);
  ctx.stroke();
  
  // Create texture from canvas
  const texture = new THREE.CanvasTexture(canvas);
  texture.needsUpdate = true;
  
  return texture;
};

// Create a pop animation sprite texture
const createPopTexture = () => {
  const canvas = document.createElement('canvas');
  canvas.width = 512;
  canvas.height = 512;
  const ctx = canvas.getContext('2d');
  
  // Create a stylized pop effect
  const gradient = ctx.createRadialGradient(256, 256, 0, 256, 256, 256);
  gradient.addColorStop(0, 'rgba(255, 255, 255, 1)');
  gradient.addColorStop(0.2, 'rgba(180, 230, 255, 0.8)');
  gradient.addColorStop(0.4, 'rgba(100, 200, 255, 0.6)');
  gradient.addColorStop(0.7, 'rgba(50, 150, 255, 0.3)');
  gradient.addColorStop(1, 'rgba(0, 100, 200, 0)');
  
  ctx.fillStyle = gradient;
  ctx.beginPath();
  ctx.arc(256, 256, 256, 0, Math.PI * 2);
  ctx.fill();
  
  // Add spiky burst lines
  ctx.strokeStyle = 'rgba(255, 255, 255, 0.9)';
  ctx.lineWidth = 8;
  
  for (let i = 0; i < 12; i++) {
    const angle = (i / 12) * Math.PI * 2;
    const innerRadius = 80;
    const outerRadius = 240;
    
    ctx.beginPath();
    ctx.moveTo(
      256 + Math.cos(angle) * innerRadius,
      256 + Math.sin(angle) * innerRadius
    );
    ctx.lineTo(
      256 + Math.cos(angle) * outerRadius,
      256 + Math.sin(angle) * outerRadius
    );
    ctx.stroke();
  }
  
  // Create texture from canvas
  const texture = new THREE.CanvasTexture(canvas);
  texture.needsUpdate = true;
  
  return texture;
};

// Create a painterly water texture
const createWaterTexture = () => {
  const canvas = document.createElement('canvas');
  canvas.width = 1024;
  canvas.height = 1024;
  const ctx = canvas.getContext('2d');
  
  // Base water color
  ctx.fillStyle = '#FCCB01';
  ctx.fillRect(0, 0, 1024, 1024);
  
  // Add painterly brush strokes
  for (let i = 0; i < 200; i++) {
    const x = Math.random() * 1024;
    const y = Math.random() * 1024;
    const width = Math.random() * 300 + 50;
    const height = Math.random() * 20 + 5;
    const angle = Math.random() * Math.PI;
    
    ctx.save();
    ctx.translate(x, y);
    ctx.rotate(angle);
    
    // Vary the brush stroke colors
    const colorVariation = Math.random() * 40 - 20;
    const r = Math.min(255, Math.max(0, 10 + colorVariation));
    const g = Math.min(255, Math.max(0, 122 + colorVariation));
    const b = Math.min(255, Math.max(0, 158 + colorVariation));
    const a = Math.random() * 0.2 + 0.05;
    
    ctx.fillStyle = `rgba(${r}, ${g}, ${b}, ${a})`;
    ctx.fillRect(-width/2, -height/2, width, height);
    ctx.restore();
  }
  
  // Add some hard-edged highlights
  for (let i = 0; i < 50; i++) {
    const x = Math.random() * 1024;
    const y = Math.random() * 1024;
    const radius = Math.random() * 100 + 20;
    
    const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);
    gradient.addColorStop(0, 'rgba(255, 255, 255, 0.2)');
    gradient.addColorStop(0.5, 'rgba(180, 230, 255, 0.1)');
    gradient.addColorStop(1, 'rgba(10, 122, 158, 0)');
    
    ctx.fillStyle = gradient;
    ctx.beginPath();
    ctx.arc(x, y, radius, 0, Math.PI * 2);
    ctx.fill();
  }
  
  // Create texture from canvas
  const texture = new THREE.CanvasTexture(canvas);
  texture.wrapS = THREE.RepeatWrapping;
  texture.wrapT = THREE.RepeatWrapping;
  texture.repeat.set(2, 2);
  texture.needsUpdate = true;
  
  return texture;
};

// Initialize audio
const initAudio = () => {
  // Create pop sound
  popSound = new Audio();
  popSound.src = 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQQAAAB9AH0AfQB9';
  popSound.volume = 0.3;
  
  // Create bubble ambient sound
  bubbleSound = new Audio();
  bubbleSound.src = 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQQAAAB9AH0AfQB9';
  bubbleSound.volume = 0.1;
  bubbleSound.loop = true;
};

// Create a pop animation at the specified position
const createPopAnimation = (position) => {
  // Create sprite material with pop texture
  const popTexture = createPopTexture();
  const spriteMaterial = new THREE.SpriteMaterial({
    map: popTexture,
    transparent: true,
    blending: THREE.AdditiveBlending
  });
  
  // Create sprite
  const sprite = new THREE.Sprite(spriteMaterial);
  sprite.position.copy(position);
  sprite.scale.set(50, 50, 1);
  scene.add(sprite);
  
  // Store creation time
  sprite.userData = { 
    creationTime: clock.getElapsedTime(),
    duration: 0.5 // Animation duration in seconds
  };
  
  // Add to pop sprites array
  popSprites.push(sprite);
  
  // Play pop sound - only after user interaction
  if (popSound && document.documentElement.hasAttribute('data-user-interacted')) {
    // Clone the sound to allow multiple pops at once
    const sound = popSound.cloneNode();
    sound.play().catch(e => console.log('Audio play prevented by browser'));
  }
};

// Update pop animations
const updatePopAnimations = () => {
  const currentTime = clock.getElapsedTime();
  
  // Update each pop sprite
  for (let i = popSprites.length - 1; i >= 0; i--) {
    const sprite = popSprites[i];
    const age = currentTime - sprite.userData.creationTime;
    const progress = age / sprite.userData.duration;
    
    if (progress >= 1) {
      // Remove completed animations
      scene.remove(sprite);
      sprite.material.dispose();
      popSprites.splice(i, 1);
    } else {
      // Scale up and fade out
      const scale = 50 + progress * 100;
      sprite.scale.set(scale, scale, 1);
      sprite.material.opacity = 1 - progress;
    }
  }
};

// Initialize the scene
const init = () => {
  const container = canvasRef.value;
  
  // Create scene
  scene = new THREE.Scene();
  
  // Create camera
  camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 1, 10000);
  camera.position.z = 1000;
  
  // Initialize raycaster for bubble interaction
  raycaster = new THREE.Raycaster();
  mouse = new THREE.Vector2();
  
  // Create water surface
  waterGeometry = new THREE.PlaneGeometry(2000, 2000, 128, 128);
  
  // Create water material with custom shader
  const waterTexture = createWaterTexture();
  waterMaterial = new THREE.ShaderMaterial({
    uniforms: {
      time: { value: 0 },
      waterTexture: { value: waterTexture },
      color: { value: new THREE.Color(props.color) }
    },
    vertexShader: `
      uniform float time;
      varying vec2 vUv;
      varying float vElevation;
      
      // Simplex noise function
      vec3 mod289(vec3 x) { return x - floor(x * (1.0 / 289.0)) * 289.0; }
      vec2 mod289(vec2 x) { return x - floor(x * (1.0 / 289.0)) * 289.0; }
      vec3 permute(vec3 x) { return mod289(((x*34.0)+1.0)*x); }
      
      float snoise(vec2 v) {
        const vec4 C = vec4(0.211324865405187, 0.366025403784439,
                 -0.577350269189626, 0.024390243902439);
        vec2 i  = floor(v + dot(v, C.yy));
        vec2 x0 = v -   i + dot(i, C.xx);
        vec2 i1;
        i1 = (x0.x > x0.y) ? vec2(1.0, 0.0) : vec2(0.0, 1.0);
        vec4 x12 = x0.xyxy + C.xxzz;
        x12.xy -= i1;
        i = mod289(i);
        vec3 p = permute(permute(i.y + vec3(0.0, i1.y, 1.0))
        + i.x + vec3(0.0, i1.x, 1.0));
        vec3 m = max(0.5 - vec3(dot(x0,x0), dot(x12.xy,x12.xy), dot(x12.zw,x12.zw)), 0.0);
        m = m*m;
        m = m*m;
        vec3 x = 2.0 * fract(p * C.www) - 1.0;
        vec3 h = abs(x) - 0.5;
        vec3 ox = floor(x + 0.5);
        vec3 a0 = x - ox;
        m *= 1.79284291400159 - 0.85373472095314 * (a0*a0 + h*h);
        vec3 g;
        g.x  = a0.x  * x0.x  + h.x  * x0.y;
        g.yz = a0.yz * x12.xz + h.yz * x12.yw;
        return 130.0 * dot(m, g);
      }
      
      void main() {
        vUv = uv;
        
        // Create stylized wave pattern
        float freq1 = 0.15;
        float freq2 = 0.08;
        float freq3 = 0.05;
        
        float amp1 = 15.0;
        float amp2 = 30.0;
        float amp3 = 10.0;
        
        // Use noise for more organic waves
        float noise1 = snoise(vec2(position.x * freq1, position.y * freq1 + time * 0.3)) * amp1;
        float noise2 = snoise(vec2(position.x * freq2 - time * 0.2, position.y * freq2)) * amp2;
        float noise3 = snoise(vec2(position.x * freq3 + time * 0.1, position.y * freq3 - time * 0.1)) * amp3;
        
        // Combine waves
        float elevation = noise1 + noise2 + noise3;
        
        // Add sharp peaks for stylized look
        elevation = elevation * (1.0 + 0.2 * sin(elevation * 0.1));
        
        // Apply elevation
        vec3 newPosition = position + vec3(0.0, 0.0, elevation);
        vElevation = elevation;
        
        // Project to screen
        gl_Position = projectionMatrix * modelViewMatrix * vec4(newPosition, 1.0);
      }
    `,
    fragmentShader: `
      uniform sampler2D waterTexture;
      uniform vec3 color;
      uniform float time;
      varying vec2 vUv;
      varying float vElevation;
      
      void main() {
        // Sample base texture
        vec4 texColor = texture2D(waterTexture, vUv + vec2(sin(time * 0.1) * 0.01, cos(time * 0.1) * 0.01));
        
        // Create cel-shaded effect with hard edges
        float light = 0.0;
        
        // Add stylized highlights based on elevation
        if (vElevation > 20.0) {
          light = 1.0; // Bright highlight on peaks
        } else if (vElevation > 10.0) {
          light = 0.7; // Medium highlight
        } else if (vElevation > 0.0) {
          light = 0.5; // Subtle highlight
        } else {
          light = 0.3; // Base level
        }
        
        // Create hard-edged highlight
        light = floor(light * 4.0) / 4.0;
        
        // Mix with base color
        vec3 finalColor = mix(color, vec3(1.0), light * 0.6);
        
        // Apply texture
        gl_FragColor = vec4(finalColor, 0.9) * texColor;
      }
    `,
    transparent: true,
    side: THREE.DoubleSide
  });
  
  // Create water mesh
  waterMesh = new THREE.Mesh(waterGeometry, waterMaterial);
  waterMesh.rotation.x = -Math.PI / 2; // Lay flat
  waterMesh.position.y = -200; // Position below camera
  scene.add(waterMesh);
  
  // Create bubbles
  bubbleGeometry = new THREE.BufferGeometry();
  
  // Initialize bubble arrays
  const count = props.particleCount;
  bubblePositions = new Float32Array(count * 3);
  bubbleVelocities = new Float32Array(count * 3);
  bubbleScales = new Float32Array(count);
  bubbleRotations = new Float32Array(count);
  bubbleStates = new Float32Array(count); // 1 = active, 0 = inactive
  
  // Initialize bubble positions and properties
  for (let i = 0; i < count; i++) {
    const i3 = i * 3;
    
    // Random position within a cylinder shape
    const radius = Math.random() * 800;
    const theta = Math.random() * Math.PI * 2;
    const y = Math.random() * 1500 - 500;
    
    bubblePositions[i3] = Math.cos(theta) * radius;
    bubblePositions[i3 + 1] = y;
    bubblePositions[i3 + 2] = Math.sin(theta) * radius;
    
    // Random velocity (mainly upward)
    bubbleVelocities[i3] = (Math.random() - 0.5) * 0.5;
    bubbleVelocities[i3 + 1] = Math.random() * 1.5 + 0.5;
    bubbleVelocities[i3 + 2] = (Math.random() - 0.5) * 0.5;
    
    // Random scale
    bubbleScales[i] = Math.random() * 1.5 + 0.5;
    
    // Random rotation
    bubbleRotations[i] = Math.random() * Math.PI * 2;
    
    // All bubbles start active
    bubbleStates[i] = 1.0;
  }
  
  // Set attributes
  bubbleGeometry.setAttribute('position', new THREE.BufferAttribute(bubblePositions, 3));
  bubbleGeometry.setAttribute('scale', new THREE.BufferAttribute(bubbleScales, 1));
  bubbleGeometry.setAttribute('rotation', new THREE.BufferAttribute(bubbleRotations, 1));
  bubbleGeometry.setAttribute('state', new THREE.BufferAttribute(bubbleStates, 1));
  
  // Create bubble material with custom shader
  const bubbleTexture = createArcaneBubbleTexture();
  bubbleMaterial = new THREE.ShaderMaterial({
    uniforms: {
      bubbleTexture: { value: bubbleTexture },
      time: { value: 0 },
      size: { value: props.particleSize }
    },
    vertexShader: `
      attribute float scale;
      attribute float rotation;
      attribute float state;
      uniform float time;
      uniform float size;
      varying float vRotation;
      varying float vState;
      
      void main() {
        vRotation = rotation;
        vState = state;
        
        // Skip rendering if bubble is inactive
        if (state < 0.5) {
          gl_Position = vec4(0.0);
          gl_PointSize = 0.0;
          return;
        }
        
        // Add wobble effect
        vec3 pos = position;
        float wobbleX = sin(time * 2.0 + position.x * 0.05) * 5.0;
        float wobbleZ = cos(time * 2.0 + position.z * 0.05) * 5.0;
        pos.x += wobbleX;
        pos.z += wobbleZ;
        
        // Project to screen
        vec4 mvPosition = modelViewMatrix * vec4(pos, 1.0);
        gl_Position = projectionMatrix * mvPosition;
        
        // Size attenuation
        gl_PointSize = size * scale * (1000.0 / -mvPosition.z);
      }
    `,
    fragmentShader: `
      uniform sampler2D bubbleTexture;
      uniform float time;
      varying float vRotation;
      varying float vState;
      
      void main() {
        // Skip rendering if bubble is inactive
        if (vState < 0.5) {
          discard;
          return;
        }
        
        // Apply rotation to texture coordinates
        float c = cos(vRotation + time * 0.5);
        float s = sin(vRotation + time * 0.5);
        vec2 rotatedUV = vec2(
          c * (gl_PointCoord.x - 0.5) + s * (gl_PointCoord.y - 0.5) + 0.5,
          c * (gl_PointCoord.y - 0.5) - s * (gl_PointCoord.x - 0.5) + 0.5
        );
        
        // Sample texture
        vec4 texColor = texture2D(bubbleTexture, rotatedUV);
        
        // Add time-based shimmer effect
        float shimmer = 0.1 * sin(time * 3.0 + vRotation * 10.0) + 0.9;
        texColor.rgb *= shimmer;
        
        gl_FragColor = texColor;
        
        // Discard transparent pixels
        if (gl_FragColor.a < 0.1) discard;
      }
    `,
    transparent: true,
    depthWrite: false,
    blending: THREE.AdditiveBlending
  });
  
  // Create bubble system
  bubbleSystem = new THREE.Points(bubbleGeometry, bubbleMaterial);
  scene.add(bubbleSystem);
  
  // Create renderer
  renderer = new THREE.WebGLRenderer({
    canvas: container,
    alpha: true,
    antialias: true,
    powerPreference: 'high-performance'
  });
  renderer.setPixelRatio(window.devicePixelRatio);
  renderer.setSize(window.innerWidth, window.innerHeight);
  renderer.toneMapping = THREE.ACESFilmicToneMapping;
  renderer.toneMappingExposure = 1.2;
  
  // Setup post-processing
  const renderScene = new RenderPass(scene, camera);
  
  const bloomPass = new UnrealBloomPass(
    new THREE.Vector2(window.innerWidth, window.innerHeight),
    props.bloomStrength,
    props.bloomRadius,
    props.bloomThreshold
  );
  
  composer = new EffectComposer(renderer);
  composer.addPass(renderScene);
  composer.addPass(bloomPass);
  
  // Initialize audio
  initAudio();
  
  // Add event listeners
  document.addEventListener('mousemove', onDocumentMouseMove);
  document.addEventListener('click', onDocumentClick);
  window.addEventListener('resize', onWindowResize);
  
  // Start animation
  animate();
  
  // We'll only play sounds after user interaction
  // This prevents the "play() failed because the user didn't interact with the document first" error
};

const onDocumentMouseMove = (event) => {
  // Update mouse position for camera rotation
  mouseX = (event.clientX - windowHalfX) * 0.05;
  mouseY = (event.clientY - windowHalfY) * 0.05;
  
  // Update normalized mouse coordinates for raycasting
  mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
  mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
};

const onDocumentClick = (event) => {
  // Mark that user has interacted with the document
  document.documentElement.setAttribute('data-user-interacted', 'true');
  
  // Try to play ambient sound after user interaction
  if (bubbleSound && !bubbleSound.playing) {
    bubbleSound.play().catch(e => console.log('Ambient audio play prevented by browser'));
    bubbleSound.playing = true;
  }
  
  // Update mouse position
  mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
  mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
  
  // Cast ray from camera through mouse position
  raycaster.setFromCamera(mouse, camera);
  
  // Check for intersections with bubbles
  const intersects = raycaster.intersectObject(bubbleSystem);
  
  if (intersects.length > 0) {
    // Get the index of the intersected bubble
    const index = intersects[0].index;
    
    // Get the position of the intersected bubble
    const position = new THREE.Vector3(
      bubblePositions[index * 3],
      bubblePositions[index * 3 + 1],
      bubblePositions[index * 3 + 2]
    );
    
    // Create pop animation at bubble position
    createPopAnimation(position);
    
    // Deactivate the bubble
    bubbleStates[index] = 0;
    bubbleSystem.geometry.attributes.state.needsUpdate = true;
    
    // Reset bubble position to bottom
    resetBubble(index);
  }
};

// Reset a bubble to the bottom
const resetBubble = (index) => {
  const i3 = index * 3;
  
  // Random position at the bottom
  const radius = Math.random() * 800;
  const theta = Math.random() * Math.PI * 2;
  
  bubblePositions[i3] = Math.cos(theta) * radius;
  bubblePositions[i3 + 1] = -500; // Start from bottom
  bubblePositions[i3 + 2] = Math.sin(theta) * radius;
  
  // Random velocity (mainly upward)
  bubbleVelocities[i3] = (Math.random() - 0.5) * 0.5;
  bubbleVelocities[i3 + 1] = Math.random() * 1.5 + 0.5;
  bubbleVelocities[i3 + 2] = (Math.random() - 0.5) * 0.5;
  
  // Random scale
  bubbleScales[index] = Math.random() * 1.5 + 0.5;
  
  // Random rotation
  bubbleRotations[index] = Math.random() * Math.PI * 2;
  
  // Reactivate after a delay
  setTimeout(() => {
    bubbleStates[index] = 1.0;
    bubbleSystem.geometry.attributes.state.needsUpdate = true;
  }, Math.random() * 2000 + 1000);
  
  // Update geometry attributes
  bubbleSystem.geometry.attributes.position.needsUpdate = true;
  bubbleSystem.geometry.attributes.scale.needsUpdate = true;
  bubbleSystem.geometry.attributes.rotation.needsUpdate = true;
};

const onWindowResize = () => {
  windowHalfX = window.innerWidth / 2;
  windowHalfY = window.innerHeight / 2;
  
  camera.aspect = window.innerWidth / window.innerHeight;
  camera.updateProjectionMatrix();
  
  renderer.setSize(window.innerWidth, window.innerHeight);
  composer.setSize(window.innerWidth, window.innerHeight);
};

const animate = () => {
  animationFrameId = requestAnimationFrame(animate);
  render();
};

const render = () => {
  const delta = clock.getDelta();
  const time = clock.getElapsedTime();
  
  // Update water shader
  if (waterMaterial) {
    waterMaterial.uniforms.time.value = time;
  }
  
  // Update bubble shader
  if (bubbleMaterial) {
    bubbleMaterial.uniforms.time.value = time;
  }
  
  // Update bubble positions
  if (bubbleSystem) {
    const positions = bubbleSystem.geometry.attributes.position.array;
    
    for (let i = 0; i < props.particleCount; i++) {
      const i3 = i * 3;
      
      // Only update active bubbles
      if (bubbleStates[i] > 0.5) {
        // Update position based on velocity
        positions[i3] += bubbleVelocities[i3] * delta * 60;
        positions[i3 + 1] += bubbleVelocities[i3 + 1] * delta * 60;
        positions[i3 + 2] += bubbleVelocities[i3 + 2] * delta * 60;
        
        // Add some wobble to velocity
        bubbleVelocities[i3] += (Math.random() - 0.5) * 0.01;
        bubbleVelocities[i3 + 2] += (Math.random() - 0.5) * 0.01;
        
        // Reset if bubble goes out of bounds
        if (positions[i3 + 1] > 800) {
          resetBubble(i);
        }
      }
    }
    
    bubbleSystem.geometry.attributes.position.needsUpdate = true;
  }
  
  // Update pop animations
  updatePopAnimations();
  
  // Rotate scene based on mouse position
  scene.rotation.x += (mouseY * 0.0005 - scene.rotation.x) * 0.01;
  scene.rotation.y += (mouseX * 0.0005 - scene.rotation.y) * 0.01;
  
  // Render scene with post-processing
  composer.render();
};

onMounted(() => {
  // Initialize animation
  init();
});

onBeforeUnmount(() => {
  // Clean up
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
  }
  
  // Remove event listeners
  document.removeEventListener('mousemove', onDocumentMouseMove);
  document.removeEventListener('click', onDocumentClick);
  window.removeEventListener('resize', onWindowResize);
  
  // Stop sounds
  if (bubbleSound) {
    bubbleSound.pause();
  }
  
  // Dispose resources
  if (renderer) {
    renderer.dispose();
  }
  
  if (waterMaterial) {
    waterMaterial.dispose();
  }
  
  if (bubbleMaterial) {
    bubbleMaterial.dispose();
  }
  
  if (waterGeometry) {
    waterGeometry.dispose();
  }
  
  if (bubbleGeometry) {
    bubbleGeometry.dispose();
  }
  
  // Clear pop sprites
  for (const sprite of popSprites) {
    scene.remove(sprite);
    sprite.material.dispose();
  }
  popSprites = [];
});
</script>

<template>
  <canvas ref="canvasRef" class="bubbly-animation"></canvas>
</template>

<style scoped>
.bubbly-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}
</style>