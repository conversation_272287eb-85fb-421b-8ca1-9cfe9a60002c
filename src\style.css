:root {
  /* New Color Palette from Template */
  --color-brown: #8B7355;          /* Brown from palette */
  --color-teal-light: #7DD3FC;     /* Light teal from palette */
  --color-teal: #0891B2;           /* Medium teal from palette */
  --color-teal-dark: #0E7490;      /* Dark teal from palette */
  --color-light-blue: #BAE6FD;     /* Light blue from palette */
  --color-white: #FFFFFF;          /* Pure white */
  --color-black: #000000;          /* Pure black */

  /* Primary brand colors */
  --color-primary: var(--color-teal);
  --color-secondary: var(--color-teal-light);
  --color-accent: var(--color-brown);

  /* Legacy color mappings for compatibility */
  --primary-dark: var(--color-teal-dark);
  --primary-light: var(--color-teal);
  --accent-dark: var(--color-brown);
  --accent-medium: var(--color-teal);
  --accent-light: var(--color-teal-light);
  --white: var(--color-white);
  --off-white: var(--color-white);
  --text-dark: var(--color-black);
  --text-light: var(--color-white);
  --text-muted: var(--color-brown);

  /* Updated color mappings */
  --color-green: var(--color-teal);
  --color-green-light: var(--color-teal-light);
  --color-green-rgb: 8, 145, 178;

  --color-blue: var(--color-teal);
  --color-blue-light: var(--color-teal-light);
  --color-blue-rgb: 8, 145, 178;

  --color-accent-light: var(--color-light-blue);
  --color-accent-rgb: 139, 115, 85;
  
  /* Typography */
  --font-heading: 'Bricolage Grotesque', 'Oswald', 'Arial Black', sans-serif;
  --font-body: 'Bricolage Grotesque', 'Work Sans', 'Arial', sans-serif;
  --font-numbers: 'Bricolage Grotesque', 'Work Sans', 'Helvetica Neue', system-ui, sans-serif;
  
  /* Base styles */
  font-family: var(--font-body);
  line-height: 1.6;
  font-weight: 400;
  color: var(--text-dark);
  background-color: var(--off-white);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scroll-behavior: smooth;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  min-height: 100vh;
  overflow-x: hidden;
  font-family: var(--font-body);
  cursor: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'><rect x='4' y='8' width='24' height='16' rx='4' fill='%23FCCB01' stroke='%23000' stroke-width='2'/><rect x='6' y='10' width='20' height='12' rx='2' fill='%23FFFF00'/><circle cx='10' cy='14' r='1' fill='%23000'/><circle cx='14' cy='16' r='1' fill='%23000'/><circle cx='18' cy='14' r='1' fill='%23000'/><circle cx='22' cy='16' r='1' fill='%23000'/></svg>") 16 16, auto;
}

/* Ensure all text elements use Bricolage Grotesque */
*, *::before, *::after {
  font-family: var(--font-body);
}

/* All inputs and forms use Bricolage Grotesque */
input, textarea, select, button {
  font-family: var(--font-body) !important;
}

/* Numbers specifically use Work Sans */
.price-amount, 
.price-dollar,
.price-main, 
.price-cents,
.stat-number,
input[type="number"],
.calculator-result,
.rate-display,
[data-numbers="true"] {
  font-family: var(--font-numbers) !important;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 1rem;
}

h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
}

h2 {
  font-size: clamp(2rem, 4vw, 3rem);
}

h3 {
  font-size: clamp(1.5rem, 3vw, 2.25rem);
}

p {
  margin-bottom: 1.5rem;
}

a {
  color: var(--primary-dark);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--accent-medium);
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.section {
  padding: 5rem 0;
}

.btn {
  display: inline-block;
  padding: 0.8rem 2rem;
  font-weight: 500;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: var(--font-body) !important;
  text-align: center;
}

.btn-primary {
  background: var(--color-secondary);
  color: var(--color-primary);
  border: 2px solid var(--color-primary);
  padding: 0.875rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-primary:hover {
  background: var(--color-primary);
  color: var(--color-secondary);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.btn-secondary {
  background-color: transparent;
  color: var(--color-primary);
  border: 2px solid var(--color-primary);
  border-radius: 50px;
  padding: 1rem 2rem;
  font-weight: 600;
}

.btn-secondary:hover {
  background-color: var(--color-secondary);
  color: var(--color-primary);
  border-color: var(--color-secondary);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.text-center {
  text-align: center;
}

.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

#app {
  width: 100%;
}

@media (max-width: 768px) {
  .section {
    padding: 3rem 0;
  }
}
