<script setup>
import { onMounted } from 'vue';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

import ThemeProvider from './components/ThemeProvider.vue';
import Navbar from './components/Navbar.vue';
import Hero from './components/Hero.vue';
import Stats from './components/Stats.vue';
import Services from './components/Services.vue';
import About from './components/About.vue';
import RateCalculator from './components/RateCalculator.vue';
import BookingForm from './components/BookingForm.vue';
import Testimonials from './components/Testimonials.vue';
import Contact from './components/Contact.vue';
import Footer from './components/Footer.vue';

gsap.registerPlugin(ScrollTrigger);

onMounted(() => {
  // Initialize scroll animations
  const fadeElements = document.querySelectorAll('.fade-in');
  
  // Create observer for fade-in elements
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
      }
    });
  }, { threshold: 0.1 });
  
  // Observe all fade-in elements
  fadeElements.forEach(element => {
    observer.observe(element);
  });
  
  // Smooth scroll for anchor links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function(e) {
      e.preventDefault();
      
      const targetId = this.getAttribute('href');
      if (targetId === '#') return;
      
      const targetElement = document.querySelector(targetId);
      if (targetElement) {
        window.scrollTo({
          top: targetElement.offsetTop - 80, // Adjust for navbar height
          behavior: 'smooth'
        });
      }
    });
  });
});
</script>

<template>
  <ThemeProvider>
    <div class="app-container">
      <Navbar />
      <main>
        <Hero />
        <Stats />
        <Services />
        <About />
        <RateCalculator />
        <BookingForm />
        <Testimonials />
        <Contact />
      </main>
      <Footer />
    </div>
  </ThemeProvider>
</template>

<style>
/* Global styles are in style.css */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

main {
  flex: 1;
}
</style>
