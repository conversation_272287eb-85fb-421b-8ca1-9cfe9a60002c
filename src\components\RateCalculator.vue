<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

// Service types with base rates per square foot
const serviceTypes = ref([
  { id: 'office', name: 'Office Cleaning', baseRate: 0.12 },
  { id: 'retail', name: 'Retail Spaces', baseRate: 0.14 },
  { id: 'medical', name: 'Medical Facilities', baseRate: 0.18 },
  { id: 'educational', name: 'Educational Institutions', baseRate: 0.13 },
  { id: 'hospitality', name: 'Hospitality', baseRate: 0.15 },
  { id: 'industrial', name: 'Industrial Cleaning', baseRate: 0.16 }
]);

// Frequency options with discount multipliers
const frequencies = ref([
  { id: 'once', name: 'One-time Service', multiplier: 1.0 },
  { id: 'weekly', name: 'Weekly', multiplier: 0.85 },
  { id: 'biweekly', name: 'Bi-weekly', multiplier: 0.9 },
  { id: 'monthly', name: 'Monthly', multiplier: 0.95 }
]);

// Form data
const calculatorData = ref({
  squareFootage: 1000,
  serviceType: 'office',
  frequency: 'once',
  addOns: []
});

// Add-on services
const addOns = ref([
  { id: 'windows', name: 'Window Cleaning', price: 50 },
  { id: 'carpet', name: 'Carpet Deep Cleaning', price: 75 },
  { id: 'disinfection', name: 'Disinfection Service', price: 60 },
  { id: 'floors', name: 'Floor Waxing/Polishing', price: 90 }
]);

// Animation ref
const rateCalculatorRef = ref(null);

// Toggle add-on selection
const toggleAddOn = (addOnId) => {
  const index = calculatorData.value.addOns.indexOf(addOnId);
  if (index === -1) {
    calculatorData.value.addOns.push(addOnId);
  } else {
    calculatorData.value.addOns.splice(index, 1);
  }
};

// Calculate the base rate
const baseRate = computed(() => {
  const service = serviceTypes.value.find(s => s.id === calculatorData.value.serviceType);
  return service ? service.baseRate : 0;
});

// Calculate frequency discount
const frequencyMultiplier = computed(() => {
  const frequency = frequencies.value.find(f => f.id === calculatorData.value.frequency);
  return frequency ? frequency.multiplier : 1;
});

// Calculate add-ons total
const addOnsTotal = computed(() => {
  return calculatorData.value.addOns.reduce((total, addOnId) => {
    const addOn = addOns.value.find(a => a.id === addOnId);
    return total + (addOn ? addOn.price : 0);
  }, 0);
});

// Calculate the estimated rate
const estimatedRate = computed(() => {
  const squareFootage = parseFloat(calculatorData.value.squareFootage) || 0;
  const baseTotal = squareFootage * baseRate.value * frequencyMultiplier.value;
  return baseTotal + addOnsTotal.value;
});

// Format currency
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2
  }).format(amount);
};

// Per visit rate (for recurring services)
const perVisitRate = computed(() => {
  if (calculatorData.value.frequency === 'once') {
    return estimatedRate.value;
  } else {
    return estimatedRate.value;
  }
});

// Monthly rate (for recurring services)
const monthlyRate = computed(() => {
  switch (calculatorData.value.frequency) {
    case 'weekly':
      return perVisitRate.value * 4;
    case 'biweekly':
      return perVisitRate.value * 2;
    case 'monthly':
      return perVisitRate.value;
    default:
      return perVisitRate.value;
  }
});

// Reset form
const resetCalculator = () => {
  calculatorData.value = {
    squareFootage: 1000,
    serviceType: 'office',
    frequency: 'once',
    addOns: []
  };
};

// Validation
const isValidInput = computed(() => {
  const squareFootage = parseFloat(calculatorData.value.squareFootage);
  return squareFootage > 0 && squareFootage <= 100000;
});

// Error message
const errorMessage = computed(() => {
  const squareFootage = parseFloat(calculatorData.value.squareFootage);
  if (isNaN(squareFootage) || squareFootage <= 0) {
    return 'Please enter a valid square footage greater than 0';
  }
  if (squareFootage > 100000) {
    return 'For spaces larger than 100,000 sq ft, please contact us directly';
  }
  return '';
});

// Animation setup
onMounted(() => {
  if (rateCalculatorRef.value) {
    // Set initial state
    gsap.set(rateCalculatorRef.value, {
      opacity: 0,
      y: 50
    });

    // Create entrance animation
    ScrollTrigger.create({
      trigger: rateCalculatorRef.value,
      start: "top 80%",
      onEnter: () => {
        gsap.to(rateCalculatorRef.value, {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: "power2.out"
        });
      }
    });
  }
});
</script>

<template>
  <section id="rate-calculator" class="section calculator" ref="rateCalculatorRef">
    <div class="container">
      <div class="section-header text-center">
        <h2>Rate Calculator</h2>
        <p class="section-subtitle">Get an instant estimate for your cleaning needs</p>
      </div>
      
      <div class="calculator-container">
        <div class="calculator-form">
          <div class="form-row">
            <div class="form-group">
              <label for="square-footage">Square Footage</label>
              <input
                type="number"
                id="square-footage"
                v-model="calculatorData.squareFootage"
                min="1"
                max="100000"
                placeholder="Enter square footage"
              >
              <p v-if="errorMessage" class="form-error">{{ errorMessage }}</p>
            </div>
            
            <div class="form-group">
              <label for="service-type">Service Type</label>
              <select id="service-type" v-model="calculatorData.serviceType">
                <option v-for="service in serviceTypes" :key="service.id" :value="service.id">
                  {{ service.name }}
                </option>
              </select>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="frequency">Cleaning Frequency</label>
              <select id="frequency" v-model="calculatorData.frequency">
                <option v-for="frequency in frequencies" :key="frequency.id" :value="frequency.id">
                  {{ frequency.name }}
                </option>
              </select>
            </div>
            
            <div class="form-group">
              <label>Additional Services</label>
              <div class="add-ons-compact">
                <div
                  v-for="addOn in addOns"
                  :key="addOn.id"
                  class="add-on-item"
                  :class="{ 'selected': calculatorData.addOns.includes(addOn.id) }"
                  @click="toggleAddOn(addOn.id)"
                >
                  <div class="add-on-checkbox">
                    <div class="checkbox-inner" v-if="calculatorData.addOns.includes(addOn.id)">✓</div>
                  </div>
                  <div class="add-on-info">
                    <span class="add-on-name">{{ addOn.name }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="form-actions">
            <button class="btn btn-secondary btn-sm" @click="resetCalculator">Reset</button>
          </div>
        </div>
        
        <div class="calculator-result">
          <div class="result-card">
            <h3>Estimated Cost</h3>
            
            <div class="result-details">
              <div class="result-item">
                <span class="result-label">Base Rate:</span>
                <span class="result-value">{{ formatCurrency(baseRate) }}/sq ft</span>
              </div>
              
              <div class="result-item" v-if="calculatorData.frequency !== 'once'">
                <span class="result-label">Frequency Discount:</span>
                <span class="result-value">{{ Math.round((1 - frequencyMultiplier) * 100) }}% off</span>
              </div>
              
              <div class="result-divider"></div>
              
              <div class="result-item total" v-if="calculatorData.frequency === 'once'">
                <span class="result-label">Total Estimate:</span>
                <span class="result-value">{{ formatCurrency(estimatedRate) }}</span>
              </div>
              
              <div v-else>
                <div class="result-item">
                  <span class="result-label">Per Visit:</span>
                  <span class="result-value">{{ formatCurrency(perVisitRate) }}</span>
                </div>
                
                <div class="result-item total">
                  <span class="result-label">Monthly Total:</span>
                  <span class="result-value">{{ formatCurrency(monthlyRate) }}</span>
                </div>
              </div>
            </div>
            
            <div class="result-cta">
              <a href="#booking" class="btn btn-primary btn-sm">Book Now</a>
              <a href="#contact" class="btn btn-secondary btn-sm">Request Quote</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped>
.calculator {
  background-color: var(--white);
  position: relative;
  padding: 3rem 0;
}

.calculator-container {
  display: grid;
  grid-template-columns: 1.2fr 0.8fr;
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.calculator-form {
  background-color: var(--off-white);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-group {
  margin-bottom: 1rem;
}

label {
  display: block;
  margin-bottom: 0.3rem;
  font-weight: 500;
  color: var(--primary-dark);
  font-size: 0.9rem;
}

input, select {
  width: 100%;
  padding: 0.6rem 0.8rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-family: var(--font-body);
  font-size: 0.9rem;
  transition: border-color 0.3s ease;
}

input:focus, select:focus {
  outline: none;
  border-color: var(--primary-dark);
}

.form-error {
  color: #e53935;
  font-size: 0.75rem;
  margin-top: 0.3rem;
  margin-bottom: 0;
}

.add-ons-compact {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.add-on-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.4rem 0.6rem;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.8rem;
}

.add-on-item:hover {
  border-color: var(--primary-dark);
  background-color: rgba(71, 47, 29, 0.05);
}

.add-on-item.selected {
  border-color: var(--primary-dark);
  background-color: rgba(71, 47, 29, 0.1);
}

.add-on-checkbox {
  width: 16px;
  height: 16px;
  border: 2px solid var(--primary-dark);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.checkbox-inner {
  color: var(--primary-dark);
  font-size: 0.7rem;
  font-weight: bold;
}

.add-on-name {
  font-size: 0.8rem;
  font-weight: 500;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 0.5rem;
}

.calculator-result {
  display: flex;
  flex-direction: column;
}

.result-card {
  background-color: var(--primary-dark);
  color: var(--white);
  border-radius: 8px;
  padding: 1.5rem;
  height: 100%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.result-card h3 {
  color: var(--primary-light);
  margin-bottom: 1rem;
  text-align: center;
  font-size: 1.4rem;
}

.result-details {
  flex: 1;
  margin-bottom: 1rem;
}

.result-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.result-label {
  font-weight: 500;
}

.result-value {
  font-weight: 600;
}

.result-divider {
  height: 1px;
  background-color: rgba(255, 255, 255, 0.2);
  margin: 0.8rem 0;
}

.result-item.total {
  font-size: 1.1rem;
  margin-top: 0.3rem;
}

.result-item.total .result-value {
  color: var(--primary-light);
  font-weight: 700;
}

.result-cta {
  display: flex;
  gap: 0.8rem;
  margin-top: 1rem;
}

.result-cta .btn {
  flex: 1;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

/* Fix secondary button visibility on dark background */
.result-cta .btn-secondary {
  background-color: transparent;
  color: var(--white);
  border: 2px solid var(--color-secondary);
}

.result-cta .btn-secondary:hover {
  background-color: var(--color-secondary);
  color: var(--color-primary);
  border-color: var(--color-secondary);
}

@media (max-width: 992px) {
  .calculator-container {
    grid-template-columns: 1fr;
  }
  
  .result-cta {
    flex-direction: row;
  }
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .add-ons-compact {
    grid-template-columns: 1fr;
  }
}
</style>