<template>
  <div class="theme-provider" :style="cssVariables">
    <slot />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { provideTheme, generateCSSVariables } from '@/composables/useTheme'

// Provide theme to all child components
const theme = provideTheme()

// Generate CSS custom properties
const cssVariables = computed(() => generateCSSVariables(theme))
</script>

<style scoped>
.theme-provider {
  width: 100%;
  min-height: 100vh;
}
</style>
