import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import './style.css'
import './styles/animations.css'
import App from './App.vue'
import { initAllAnimations } from './utils/animations.js'

// Create a simple router for future expansion
const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: App
    }
  ],
  scrollBehavior() {
    // Always scroll to top
    return { top: 0 }
  }
})

const app = createApp(App)
app.use(router)
app.mount('#app')

// Initialize animations after the app is mounted
setTimeout(() => {
  initAllAnimations()
}, 100)
