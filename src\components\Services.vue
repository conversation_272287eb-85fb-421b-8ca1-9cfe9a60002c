<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

// Track active service for hover effects
const activeService = ref(null);
const servicesSection = ref(null);
const servicesTrack = ref(null);
const serviceCards = ref([]);
const sectionHeader = ref(null);

// Service hover effect
const setActiveService = (id) => {
  activeService.value = id;
};

const clearActiveService = () => {
  activeService.value = null;
};

// Services data
const services = ref([
  {
    id: 1,
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="36" height="36" fill="currentColor"><path d="M12 7V3H2v18h20V7H12zM6 19H4v-2h2v2zm0-4H4v-2h2v2zm0-4H4V9h2v2zm0-4H4V5h2v2zm4 12H8v-2h2v2zm0-4H8v-2h2v2zm0-4H8V9h2v2zm0-4H8V5h2v2zm10 12h-8v-2h2v-2h-2v-2h2v-2h-2V9h8v10zm-2-8h-2v2h2v-2zm0 4h-2v2h2v-2z"/></svg>`,
    title: 'Office Cleaning',
    description: 'Comprehensive cleaning solutions for offices of all sizes, ensuring a pristine work environment for your team and clients.',
    image: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=400&fit=crop&crop=center'
  },
  {
    id: 2,
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="36" height="36" fill="currentColor"><path d="M21.9 8.89l-1.05-4.37c-.22-.9-1-1.52-1.91-1.52H5.05c-.9 0-1.69.63-1.9 1.52L2.1 8.89c-.24 1.02-.02 2.06.62 **********.19.19.28.29V19c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-6.94c.09-.09.2-.18.28-.28.64-.82.87-1.87.62-2.89zm-2.99-3.9l1.05 4.37c.**********-.25 1.17-.14.18-.44.47-.94.47-.61 0-1.14-.49-1.21-1.14L16.98 5l1.93-.01zM13 5h1.96l.54 4.52c.05.39-.07.78-.33 1.07-.22.26-.54.41-.95.41-.67 0-1.22-.59-1.22-1.31V5zM8.49 9.52L9.04 5H11v4.69c0 .72-.55 1.31-1.29 1.31-.34 0-.65-.15-.89-.41-.25-.29-.37-.68-.33-1.07zm-4.45-.16L5.05 5h1.97l-.58 4.86c-.08.65-.6 1.14-1.21 1.14-.49 0-.8-.29-.93-.47-.27-.32-.36-.75-.26-1.17zM5 19v-6.03c.***********.23.03.87 0 1.66-.36 2.24-.95.6.6 1.4.95 ********** 0 1.65-.36 2.23-.93.59.57 1.39.93 ********** 0 1.64-.35 2.24-.95.58.59 1.37.95 ********** 0 .15-.02.23-.03V19H5z"/></svg>`,
    title: 'Retail Spaces',
    description: 'Specialized cleaning services for retail environments, focusing on customer areas, displays, and high-touch surfaces.',
    image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600&h=400&fit=crop&crop=center'
  },
  {
    id: 3,
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="36" height="36" fill="currentColor"><path d="M19 3H5c-1.1 0-1.99.9-1.99 2L3 19c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-1 11h-4v4h-4v-4H6v-4h4V6h4v4h4v4z"/></svg>`,
    title: 'Medical Facilities',
    description: 'Sanitization and disinfection services meeting strict healthcare standards for clinics, dental offices, and medical centers.',
    image: 'https://images.unsplash.com/photo-1519494026892-80bbd2d6fd0d?w=600&h=400&fit=crop&crop=center'
  },
  {
    id: 4,
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="36" height="36" fill="currentColor"><path d="M5 13.18v4L12 21l7-3.82v-4L12 17l-7-3.82zM12 3L1 9l11 6 9-4.91V17h2V9L12 3z"/></svg>`,
    title: 'Educational Institutions',
    description: 'Tailored cleaning programs for schools, colleges, and universities, creating healthy learning environments.',
    image: 'https://images.unsplash.com/photo-**********-701939374585?w=600&h=400&fit=crop&crop=center'
  },
  {
    id: 5,
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="36" height="36" fill="currentColor"><path d="M7 13c1.66 0 3-1.34 3-3S8.66 7 7 7s-3 1.34-3 3 1.34 3 3 3zm12-6h-8v7H3V5H1v15h2v-3h18v3h2v-9c0-2.21-1.79-4-4-4z"/></svg>`,
    title: 'Hospitality',
    description: 'Premium cleaning services for hotels, restaurants, and event spaces, enhancing guest experiences through immaculate environments.',
    image: 'https://images.unsplash.com/photo-1564501049412-61c2a3083791?w=600&h=400&fit=crop&crop=center'
  },
  {
    id: 6,
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="36" height="36" fill="currentColor"><path d="M12 7V3H2v18h20V7H12zM6 19H4v-2h2v2zm0-4H4v-2h2v2zm0-4H4V9h2v2zm0-4H4V5h2v2zm4 12H8v-2h2v2zm0-4H8v-2h2v2zm0-4H8V9h2v2zm0-4H8V5h2v2zm10 12h-8v-2h2v-2h-2v-2h2v-2h-2V9h8v10zm-2-8h-2v2h2v-2zm0 4h-2v2h2v-2z"/></svg>`,
    title: 'Industrial Cleaning',
    description: 'Heavy-duty cleaning solutions for warehouses, manufacturing facilities, and industrial complexes.',
    image: 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?w=600&h=400&fit=crop&crop=center'
  }
]);

onMounted(() => {
  // Simple scroll animations without horizontal scroll hijacking
  
  // Animate section header
  gsap.from(sectionHeader.value, {
    scrollTrigger: {
      trigger: sectionHeader.value,
      start: 'top 80%',
    },
    opacity: 0,
    y: -30,
    duration: 1,
    ease: 'power2.out'
  });
  
  // Animate service cards
  serviceCards.value.forEach((card, index) => {
    gsap.from(card, {
      scrollTrigger: {
        trigger: card,
        start: 'top 85%'
      },
      opacity: 0,
      y: 30,
      scale: 0.95,
      delay: index * 0.1,
      duration: 0.8,
      ease: 'back.out(1.2)'
    });
  });
  
  // Animate CTA
  gsap.from('.services-cta', {
    scrollTrigger: {
      trigger: '.services-cta',
      start: 'top 85%',
    },
    opacity: 0,
    y: 30,
    duration: 1,
    ease: 'power2.out'
  });
  
  // Update ScrollTrigger on window resize
  window.addEventListener('resize', () => {
    ScrollTrigger.refresh();
  });
});

// Clean up
onBeforeUnmount(() => {
  // Kill all ScrollTrigger instances to prevent memory leaks
  ScrollTrigger.getAll().forEach(trigger => trigger.kill());
});
</script>

<template>
  <section id="services" class="section services" ref="servicesSection">
    <div class="sticky-container">
      <div class="section-header text-center" ref="sectionHeader">
        <h2 class="section-title">Our Premium Services</h2>
        <p class="section-subtitle">
          Tailored cleaning solutions to meet the unique needs of your business
        </p>
      </div>

      <div class="services-track-container">
        <div class="services-track" ref="servicesTrack">
          <div
            v-for="(service, index) in services"
            :key="service.id"
            class="service-card"
            :class="{ 'active': activeService === service.id }"
            @mouseenter="setActiveService(service.id)"
            @mouseleave="clearActiveService()"
            ref="serviceCards"
          >
            <div class="service-image">
              <img :src="service.image" :alt="service.title" />
            </div>
            <div class="service-content">
              <h3 class="service-title">{{ service.title }}</h3>
              <p class="service-description">{{ service.description }}</p>
              <a href="#contact" class="service-link">
                <span>Learn More</span>
                <span class="link-arrow">→</span>
              </a>
            </div>
            <div class="card-background"></div>
          </div>
        </div>
        

      </div>
    </div>
    
    <div class="services-cta text-center">
      <div class="cta-content">
        <h3>Need a customized cleaning solution?</h3>
        <p>We offer tailored services to meet your specific requirements</p>
        <a href="#contact" class="btn btn-primary">
          <span>Contact Us</span>
          <span class="btn-icon">→</span>
        </a>
      </div>
    </div>
  </section>
</template>

<style scoped>
.services {
  background-color: var(--off-white);
  position: relative;
  overflow: visible;
  padding: 4rem 0;
}

.sticky-container {
  position: relative;
  width: 100%;
  overflow: visible;
}

.section-header {
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;
  padding: 0 2rem;
}

.section-title {
  position: relative;
  display: inline-block;
  margin-bottom: 1rem;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-dark), var(--color-green));
  border-radius: 3px;
}

.section-subtitle {
  font-size: 1.2rem;
  color: var(--text-muted);
  max-width: 700px;
  margin: 0 auto;
}

.services-track-container {
  position: relative;
  width: 100%;
  overflow: visible;
  padding: 2rem 0;
}

.services-track {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  padding: 1rem 2rem;
  max-width: 1400px;
  margin: 0 auto;
}



@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.service-card {
  background-color: var(--white);
  border-radius: 16px;
  padding: 1.75rem 2rem;
  box-shadow:
    0 15px 40px rgba(0, 0, 0, 0.08),
    0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: visible;
  transform-style: preserve-3d;
  will-change: transform, box-shadow;
  z-index: 1;
  min-height: 450px;
  display: flex;
  flex-direction: column;
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(179, 197, 215, 0.05) 0%, rgba(212, 175, 55, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: -1;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 0;
  background: linear-gradient(to bottom, var(--primary-dark), var(--color-green));
  transition: height 0.5s cubic-bezier(0.19, 1, 0.22, 1);
  z-index: 0;
}

.service-card {
  border: 2px solid transparent;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275), border-color 0.3s ease;
}

.service-card:hover,
.service-card.active {
  transform: translateY(-15px) scale(1.02);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 5px 10px rgba(0, 0, 0, 0.05);
  border-color: var(--color-green);
}

.service-card:hover .card-background,
.service-card.active .card-background {
  opacity: 1;
}

.service-image {
  margin-bottom: 1.5rem;
  width: 100%;
  height: 240px;
  overflow: hidden;
  border-radius: 10px;
  transition: transform 0.4s ease;
}

.service-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.6s ease;
}

.service-card:hover .service-image img,
.service-card.active .service-image img {
  transform: scale(1.1);
}

.service-content {
  position: relative;
  z-index: 2;
}

.service-title {
  font-size: 1.8rem;
  margin-bottom: 1.25rem;
  color: var(--primary-dark);
  transition: color 0.3s ease;
  position: relative;
}

.service-card:hover .service-title,
.service-card.active .service-title {
  color: var(--accent-dark);
}

.service-description {
  color: var(--text-muted);
  margin-bottom: 1.75rem;
  line-height: 1.7;
  font-size: 1.1rem;
}

.service-link {
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  position: relative;
  color: var(--primary-dark);
  transition: color 0.3s ease;
  font-size: 1.1rem;
  margin-top: auto;
}

.link-arrow {
  margin-left: 5px;
  transition: transform 0.3s ease;
}

.service-link:hover {
  color: var(--color-green);
}

.service-link:hover .link-arrow {
  transform: translateX(5px);
}

.services-cta {
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--color-green-light) 100%);
  padding: 0;
  border-radius: 12px;
  margin: 4rem auto;
  max-width: 1200px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.services-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.5;
}

.cta-content {
  padding: 3.5rem;
  position: relative;
  z-index: 1;
}

.services-cta h3 {
  color: var(--primary-dark);
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.services-cta p {
  margin-bottom: 1.5rem;
  color: var(--accent-dark);
  font-size: 1.1rem;
}

.services-cta .btn-primary {
  background: linear-gradient(135deg, var(--primary-dark), var(--color-green));
  border: none;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 2rem;
  transition: all 0.3s ease;
}

.services-cta .btn-primary:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.3);
}

.btn-icon {
  transition: transform 0.3s ease;
}

.btn-primary:hover .btn-icon {
  transform: translateX(5px);
}

@media (max-width: 992px) {
  .services-track {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
  }

  .service-card {
    height: 400px;
    padding: 1.5rem 1.75rem;
  }

  .service-image {
    height: 180px;
  }

  .service-title {
    font-size: 1.6rem;
  }
}

@media (max-width: 768px) {
  .services-track {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1rem;
  }

  .service-card {
    height: 350px;
    padding: 1.25rem 1.5rem;
  }

  .cta-content {
    padding: 2rem;
  }

  .service-card:hover,
  .service-card.active {
    transform: translateY(-10px) scale(1.01);
  }

  .service-image {
    height: 140px;
  }

  .service-title {
    font-size: 1.4rem;
  }

  .service-description {
    font-size: 1rem;
  }


}
</style>