<script setup>
import { ref, computed, onMounted } from 'vue';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const bookingData = ref({
  // Contact Information
  name: '',
  email: '',
  phone: '',
  companyName: '',
  
  // Property Details
  propertyType: 'office',
  squareFootage: '',
  address: '',
  city: '',
  state: '',
  zipCode: '',
  
  // Service Details
  serviceType: 'Office Cleaning',
  frequency: 'one-time',
  preferredStartDate: '',
  preferredTime: 'morning',
  
  // Special Requirements
  specialInstructions: '',
  additionalServices: []
});

const isSubmitting = ref(false);
const isSubmitted = ref(false);
const errorMessage = ref('');

// Animation ref
const bookingFormRef = ref(null);

// Property types
const propertyTypes = ref([
  { id: 'office', name: 'Office Space' },
  { id: 'retail', name: 'Retail Store' },
  { id: 'medical', name: 'Medical Facility' },
  { id: 'educational', name: 'Educational Institution' },
  { id: 'hospitality', name: 'Hospitality' },
  { id: 'industrial', name: 'Industrial Facility' },
  { id: 'other', name: 'Other' }
]);

// Service types
const serviceTypes = ref([
  'Office Cleaning',
  'Retail Spaces',
  'Medical Facilities',
  'Educational Institutions',
  'Hospitality',
  'Industrial Cleaning',
  'Other'
]);

// Frequency options
const frequencyOptions = ref([
  { id: 'one-time', name: 'One-time Service' },
  { id: 'weekly', name: 'Weekly' },
  { id: 'biweekly', name: 'Bi-weekly' },
  { id: 'monthly', name: 'Monthly' },
  { id: 'custom', name: 'Custom Schedule' }
]);

// Time preferences
const timePreferences = ref([
  { id: 'morning', name: 'Morning (8am - 12pm)' },
  { id: 'afternoon', name: 'Afternoon (12pm - 5pm)' },
  { id: 'evening', name: 'Evening (5pm - 9pm)' },
  { id: 'night', name: 'Night (After Hours)' },
  { id: 'flexible', name: 'Flexible' }
]);

// Additional services
const additionalServices = ref([
  { id: 'windows', name: 'Window Cleaning' },
  { id: 'carpet', name: 'Carpet Deep Cleaning' },
  { id: 'disinfection', name: 'Disinfection Service' },
  { id: 'floors', name: 'Floor Waxing/Polishing' },
  { id: 'pressure', name: 'Pressure Washing' },
  { id: 'waste', name: 'Waste Management' }
]);

// Toggle additional service selection
const toggleAdditionalService = (serviceId) => {
  const index = bookingData.value.additionalServices.indexOf(serviceId);
  if (index === -1) {
    bookingData.value.additionalServices.push(serviceId);
  } else {
    bookingData.value.additionalServices.splice(index, 1);
  }
};

// Form validation
const isFormValid = computed(() => {
  return (
    bookingData.value.name.trim() !== '' &&
    bookingData.value.email.trim() !== '' &&
    bookingData.value.phone.trim() !== '' &&
    bookingData.value.address.trim() !== '' &&
    bookingData.value.city.trim() !== '' &&
    bookingData.value.state.trim() !== '' &&
    bookingData.value.zipCode.trim() !== '' &&
    bookingData.value.squareFootage.trim() !== '' &&
    bookingData.value.preferredStartDate !== ''
  );
});

// Submit form
const submitBooking = () => {
  if (!isFormValid.value) {
    errorMessage.value = 'Please fill in all required fields';
    return;
  }
  
  isSubmitting.value = true;
  errorMessage.value = '';
  
  // Simulate form submission
  setTimeout(() => {
    isSubmitting.value = false;
    isSubmitted.value = true;
    
    // Reset form after submission
    bookingData.value = {
      name: '',
      email: '',
      phone: '',
      companyName: '',
      propertyType: 'office',
      squareFootage: '',
      address: '',
      city: '',
      state: '',
      zipCode: '',
      serviceType: 'Office Cleaning',
      frequency: 'one-time',
      preferredStartDate: '',
      preferredTime: 'morning',
      specialInstructions: '',
      additionalServices: []
    };
    
    // Show success message for 5 seconds
    setTimeout(() => {
      isSubmitted.value = false;
    }, 5000);
  }, 1500);
};

// Get today's date in YYYY-MM-DD format for min date attribute
const today = new Date().toISOString().split('T')[0];

// Animation setup
onMounted(() => {
  if (bookingFormRef.value) {
    // Set initial state
    gsap.set(bookingFormRef.value, {
      opacity: 0,
      y: 50
    });

    // Create entrance animation
    ScrollTrigger.create({
      trigger: bookingFormRef.value,
      start: "top 80%",
      onEnter: () => {
        gsap.to(bookingFormRef.value, {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: "power2.out"
        });
      }
    });
  }
});
</script>

<template>
  <section id="booking" class="section booking" ref="bookingFormRef">
    <div class="container">
      <div class="section-header text-center">
        <h2>Book Our Services</h2>
      </div>
      
      <div class="booking-form-container">
        <form class="booking-form" @submit.prevent="submitBooking">
          <div class="form-success" v-if="isSubmitted">
            <div class="success-icon">✓</div>
            <h3>Booking Request Submitted!</h3>
            <p>Thank you for choosing CCWEB Cleaning. We'll contact you within 24 hours to confirm your booking details.</p>
          </div>
          
          <div v-else>
            <div class="form-tabs">
              <div class="form-tab-container">
                <div class="form-tab active">
                  <span>Contact & Property</span>
                </div>
                <div class="form-tab active">
                  <span>Service Details</span>
                </div>
              </div>
            </div>
            
            <div class="form-content">
              <!-- Contact & Property Section -->
              <div class="form-section">
                <div class="form-row">
                  <div class="form-group">
                    <label for="name">Full Name <span class="required">*</span></label>
                    <input
                      type="text"
                      id="name"
                      v-model="bookingData.name"
                      required
                      placeholder="Your name"
                    >
                  </div>
                  
                  <div class="form-group">
                    <label for="email">Email <span class="required">*</span></label>
                    <input
                      type="email"
                      id="email"
                      v-model="bookingData.email"
                      required
                      placeholder="Your email"
                    >
                  </div>
                </div>
                
                <div class="form-row">
                  <div class="form-group">
                    <label for="phone">Phone <span class="required">*</span></label>
                    <input
                      type="tel"
                      id="phone"
                      v-model="bookingData.phone"
                      required
                      placeholder="Your phone number"
                    >
                  </div>
                  
                  <div class="form-group">
                    <label for="property-type">Property Type <span class="required">*</span></label>
                    <select id="property-type" v-model="bookingData.propertyType" required>
                      <option v-for="type in propertyTypes" :key="type.id" :value="type.id">
                        {{ type.name }}
                      </option>
                    </select>
                  </div>
                </div>
                
                <div class="form-row">
                  <div class="form-group">
                    <label for="square-footage">Square Footage <span class="required">*</span></label>
                    <input
                      type="number"
                      id="square-footage"
                      v-model="bookingData.squareFootage"
                      required
                      placeholder="Approximate sq ft"
                      min="1"
                    >
                  </div>
                  
                  <div class="form-group">
                    <label for="address">Address <span class="required">*</span></label>
                    <input
                      type="text"
                      id="address"
                      v-model="bookingData.address"
                      required
                      placeholder="Street address"
                    >
                  </div>
                </div>
                
                <div class="form-row">
                  <div class="form-group">
                    <label for="city">City <span class="required">*</span></label>
                    <input
                      type="text"
                      id="city"
                      v-model="bookingData.city"
                      required
                      placeholder="City"
                    >
                  </div>
                  
                  <div class="form-group">
                    <label for="state">State <span class="required">*</span></label>
                    <input
                      type="text"
                      id="state"
                      v-model="bookingData.state"
                      required
                      placeholder="State"
                    >
                  </div>
                  
                  <div class="form-group">
                    <label for="zip-code">Zip <span class="required">*</span></label>
                    <input
                      type="text"
                      id="zip-code"
                      v-model="bookingData.zipCode"
                      required
                      placeholder="Zip code"
                    >
                  </div>
                </div>
              </div>
              
              <!-- Service Details Section -->
              <div class="form-section">
                <div class="form-row">
                  <div class="form-group">
                    <label for="service-type">Service Type <span class="required">*</span></label>
                    <select id="service-type" v-model="bookingData.serviceType" required>
                      <option v-for="service in serviceTypes" :key="service" :value="service">
                        {{ service }}
                      </option>
                    </select>
                  </div>
                  
                  <div class="form-group">
                    <label for="frequency">Frequency <span class="required">*</span></label>
                    <select id="frequency" v-model="bookingData.frequency" required>
                      <option v-for="option in frequencyOptions" :key="option.id" :value="option.id">
                        {{ option.name }}
                      </option>
                    </select>
                  </div>
                </div>
                
                <div class="form-row">
                  <div class="form-group">
                    <label for="preferred-date">Start Date <span class="required">*</span></label>
                    <input
                      type="date"
                      id="preferred-date"
                      v-model="bookingData.preferredStartDate"
                      required
                      :min="today"
                    >
                  </div>
                  
                  <div class="form-group">
                    <label for="preferred-time">Preferred Time <span class="required">*</span></label>
                    <select id="preferred-time" v-model="bookingData.preferredTime" required>
                      <option v-for="time in timePreferences" :key="time.id" :value="time.id">
                        {{ time.name }}
                      </option>
                    </select>
                  </div>
                </div>
                
                <div class="form-group">
                  <label>Additional Services (Optional)</label>
                  <div class="additional-services-grid">
                    <div
                      v-for="service in additionalServices"
                      :key="service.id"
                      class="additional-service-item"
                      :class="{ 'selected': bookingData.additionalServices.includes(service.id) }"
                      @click="toggleAdditionalService(service.id)"
                    >
                      <div class="service-checkbox">
                        <div class="checkbox-inner" v-if="bookingData.additionalServices.includes(service.id)">✓</div>
                      </div>
                      <div class="service-name">{{ service.name }}</div>
                    </div>
                  </div>
                </div>
                
                <div class="form-group">
                  <label for="special-instructions">Special Instructions</label>
                  <textarea
                    id="special-instructions"
                    v-model="bookingData.specialInstructions"
                    rows="2"
                    placeholder="Tell us about any specific requirements"
                  ></textarea>
                </div>
              </div>
            </div>
            
            <div class="form-error" v-if="errorMessage">
              {{ errorMessage }}
            </div>
            
            <div class="form-actions">
              <button
                type="submit"
                class="btn btn-primary submit-btn"
                :disabled="isSubmitting || !isFormValid"
              >
                <span v-if="isSubmitting">Processing...</span>
                <span v-else>Submit Booking Request</span>
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </section>
</template>

<style scoped>
.booking {
  background-color: var(--white);
  position: relative;
  padding: 3rem 0;
}

.booking-form-container {
  max-width: 800px;
  margin: 0 auto;
  background-color: var(--off-white);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.booking-form {
  padding: 1.5rem;
}

.form-tabs {
  margin-bottom: 1.5rem;
}

.form-tab-container {
  display: flex;
  justify-content: space-around;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.form-tab {
  padding: 0.8rem 1rem;
  font-weight: 500;
  color: var(--text-muted);
  position: relative;
  text-align: center;
  flex: 1;
}

.form-tab.active {
  color: var(--primary-dark);
}

.form-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--primary-dark);
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-section {
  margin-bottom: 1rem;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-row .form-group {
  margin-bottom: 0;
}

label {
  display: block;
  margin-bottom: 0.3rem;
  font-weight: 500;
  color: var(--primary-dark);
  font-size: 0.9rem;
}

.required {
  color: #e53935;
  margin-left: 2px;
}

input, select, textarea {
  width: 100%;
  padding: 0.6rem 0.8rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-family: var(--font-body);
  font-size: 0.9rem;
  transition: border-color 0.3s ease;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--primary-dark);
}

.additional-services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 0.8rem;
  margin-top: 0.3rem;
}

.additional-service-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.additional-service-item:hover {
  border-color: var(--primary-dark);
  background-color: rgba(71, 47, 29, 0.05);
}

.additional-service-item.selected {
  border-color: var(--primary-dark);
  background-color: rgba(71, 47, 29, 0.1);
}

.service-checkbox {
  width: 16px;
  height: 16px;
  border: 2px solid var(--primary-dark);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.checkbox-inner {
  color: var(--primary-dark);
  font-size: 0.7rem;
  font-weight: bold;
}

.service-name {
  font-size: 0.8rem;
  font-weight: 500;
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
}

.submit-btn {
  padding: 0.8rem 2rem;
  font-size: 1rem;
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.form-error {
  color: #e53935;
  margin-bottom: 0.8rem;
  font-size: 0.8rem;
  text-align: center;
}

.form-success {
  text-align: center;
  padding: 1.5rem 0;
}

.success-icon {
  background-color: var(--color-secondary);
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5rem;
  margin: 0 auto 1rem;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .additional-services-grid {
    grid-template-columns: 1fr;
  }
}
</style>