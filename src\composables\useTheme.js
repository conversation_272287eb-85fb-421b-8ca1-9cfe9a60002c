import { reactive, provide, inject } from 'vue'

// Theme colors from the actual palette
const themeColors = {
  // Primary colors from the actual palette
  yellow: '#FCCB01',      // #FCCB01 - Golden yellow
  brightYellow: '#FFFF00', // #FFFF00 - Bright yellow
  black: '#000000',       // #000000 - Pure black
  white: '#FFFFFF'        // #FFFFFF - Pure white
}

// Theme configuration
const createTheme = () => {
  return reactive({
    colors: {
      // Primary brand colors
      primary: themeColors.black,
      primaryLight: themeColors.black,
      secondary: themeColors.yellow,
      accent: themeColors.brightYellow,
      
      // Semantic colors
      background: themeColors.white,
      surface: themeColors.white,
      text: {
        primary: themeColors.black,
        secondary: themeColors.black,
        light: themeColors.white,
        muted: themeColors.black
      },
      
      // State colors
      success: themeColors.yellow,
      warning: themeColors.brightYellow,
      info: themeColors.yellow,
      
      // Raw palette access
      palette: themeColors
    },
    
    // Typography
    typography: {
      fontFamily: {
        heading: "'Bricolage Grotesque', 'Oswald', 'Arial Black', sans-serif",
        body: "'Bricolage Grotesque', 'Work Sans', 'Arial', sans-serif"
      },
      fontSize: {
        xs: '0.75rem',
        sm: '0.875rem',
        base: '1rem',
        lg: '1.125rem',
        xl: '1.25rem',
        '2xl': '1.5rem',
        '3xl': '1.875rem',
        '4xl': '2.25rem',
        '5xl': '3rem',
        '6xl': '3.75rem',
        '7xl': '4.5rem',
        '8xl': '6rem',
        '9xl': '8rem'
      }
    },
    
    // Spacing
    spacing: {
      xs: '0.5rem',
      sm: '1rem',
      md: '1.5rem',
      lg: '2rem',
      xl: '3rem',
      '2xl': '4rem',
      '3xl': '6rem'
    },
    
    // Border radius
    borderRadius: {
      sm: '0.25rem',
      md: '0.5rem',
      lg: '0.75rem',
      xl: '1rem',
      full: '9999px'
    },
    
    // Shadows
    shadows: {
      sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
      md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
    }
  })
}

// Theme symbols for provide/inject
const ThemeSymbol = Symbol('theme')

// Theme provider composable
export function provideTheme() {
  const theme = createTheme()
  provide(ThemeSymbol, theme)
  return theme
}

// Theme consumer composable
export function useTheme() {
  const theme = inject(ThemeSymbol)
  if (!theme) {
    throw new Error('useTheme must be used within a theme provider')
  }
  return theme
}

// CSS custom properties generator
export function generateCSSVariables(theme) {
  return {
    '--color-primary': theme.colors.primary,
    '--color-primary-light': theme.colors.primaryLight,
    '--color-secondary': theme.colors.secondary,
    '--color-accent': theme.colors.accent,
    '--color-background': theme.colors.background,
    '--color-surface': theme.colors.surface,
    '--color-text-primary': theme.colors.text.primary,
    '--color-text-secondary': theme.colors.text.secondary,
    '--color-text-light': theme.colors.text.light,
    '--color-text-muted': theme.colors.text.muted,
    '--color-success': theme.colors.success,
    '--color-warning': theme.colors.warning,
    '--color-info': theme.colors.info,
    '--font-heading': theme.typography.fontFamily.heading,
    '--font-body': theme.typography.fontFamily.body,
    '--shadow-sm': theme.shadows.sm,
    '--shadow-md': theme.shadows.md,
    '--shadow-lg': theme.shadows.lg,
    '--shadow-xl': theme.shadows.xl
  }
}
