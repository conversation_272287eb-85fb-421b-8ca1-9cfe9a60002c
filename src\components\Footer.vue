<script setup>
import { onMounted } from 'vue';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

onMounted(() => {
  // Set initial state for footer elements
  gsap.set('.footer-content > *', {
    opacity: 0,
    y: 20
  });

  // Animate footer elements on scroll
  ScrollTrigger.create({
    trigger: '.footer',
    start: 'top 90%',
    onEnter: () => {
      gsap.to('.footer-content > *', {
        opacity: 1,
        y: 0,
        stagger: 0.1,
        duration: 0.8,
        ease: 'power2.out'
      });
    }
  });
});
</script>

<template>
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-logo">
          <a href="#" class="logo-link">
            <span class="logo-text">Clear Choice </span><span class="logo-accent">Cleaners</span>
          </a>
          <p>Premium commercial cleaning services for businesses that demand excellence.</p>
        </div>
        
        <div class="footer-links">
          <h4>Quick Links</h4>
          <ul>
            <li><a href="#home">Home</a></li>
            <li><a href="#services">Services</a></li>
            <li><a href="#about">About Us</a></li>
            <li><a href="#testimonials">Testimonials</a></li>
            <li><a href="#contact">Contact</a></li>
          </ul>
        </div>
        
        <div class="footer-services">
          <h4>Our Services</h4>
          <ul>
            <li><a href="#services">Office Cleaning</a></li>
            <li><a href="#services">Retail Spaces</a></li>
            <li><a href="#services">Medical Facilities</a></li>
            <li><a href="#services">Educational Institutions</a></li>
            <li><a href="#services">Hospitality</a></li>
            <li><a href="#services">Industrial Cleaning</a></li>
          </ul>
        </div>
        
        <div class="footer-contact">
          <h4>Contact Us</h4>
          <ul>
            <li>
              <span class="icon">📞</span>
              <span>+1 (403) 383-5408</span>
            </li>
            <li>
              <span class="icon">✉️</span>
              <span><EMAIL></span>
            </li>
            <li>
              <span class="icon">🏢</span>
              <span>40 Hopewell Way Unit #10<br>Calgary, AB, T3J 5H7</span>
            </li>
          </ul>
        </div>
      </div>
      
      <div class="footer-bottom">
        <div class="copyright">
          &copy; {{ new Date().getFullYear() }} Clear Choice Cleaners. All rights reserved.
        </div>
        <div class="social-links">
          <a href="#" class="social-link" aria-label="Facebook">
            <span class="social-icon">f</span>
          </a>
          <a href="#" class="social-link" aria-label="Twitter">
            <span class="social-icon">t</span>
          </a>
          <a href="#" class="social-link" aria-label="Instagram">
            <span class="social-icon">i</span>
          </a>
          <a href="#" class="social-link" aria-label="LinkedIn">
            <span class="social-icon">in</span>
          </a>
        </div>
      </div>
    </div>
  </footer>
</template>

<style scoped>
.footer {
  background-color: var(--primary-dark);
  color: var(--text-light);
  padding: 5rem 0 2rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 3rem;
  margin-bottom: 4rem;
}

.footer-logo .logo-link {
  font-family: var(--font-heading);
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 1rem;
  display: inline-block;
}

.logo-text {
  color: var(--white);
}

.logo-accent {
  color: var(--color-secondary);
}

.footer-logo p {
  color: var(--color-accent);
  margin-top: 1rem;
  line-height: 1.6;
}

.footer-links h4,
.footer-services h4,
.footer-contact h4 {
  color: var(--white);
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
}

.footer-links ul,
.footer-services ul,
.footer-contact ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li,
.footer-services li {
  margin-bottom: 0.8rem;
}

.footer-contact li {
  display: flex;
  gap: 0.8rem;
  margin-bottom: 1rem;
}

.footer-links a,
.footer-services a {
  color: var(--color-accent);
  transition: color 0.3s ease, transform 0.3s ease;
  display: inline-block;
}

.footer-links a:hover,
.footer-services a:hover {
  color: var(--color-secondary);
  transform: translateX(5px);
}

.icon {
  font-size: 1.2rem;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.copyright {
  color: var(--color-accent);
  font-size: 0.9rem;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--white);
  transition: all 0.3s ease;
}

.social-link:hover {
  background-color: var(--primary-light);
  color: var(--primary-dark);
  transform: translateY(-3px);
}

.social-icon {
  font-size: 1.2rem;
  font-weight: 700;
}

@media (max-width: 992px) {
  .footer-content {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .footer-content {
    grid-template-columns: 1fr;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}
</style>