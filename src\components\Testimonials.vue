<script setup>
import { ref, onMounted } from 'vue';
import { gsap } from 'gsap';

const testimonials = ref([
  {
    id: 1,
    name: '<PERSON>',
    position: 'Office Manager, TechCorp',
    image: 'https://randomuser.me/api/portraits/women/44.jpg',
    quote: 'CCWEB has transformed our office environment. Their attention to detail and consistent quality have made a noticeable difference. Our team is happier and more productive in a cleaner space.'
  },
  {
    id: 2,
    name: '<PERSON>',
    position: 'Director, Eastside Medical Center',
    image: 'https://randomuser.me/api/portraits/men/32.jpg',
    quote: 'As a medical facility, cleanliness is paramount. CCWEB understands our strict requirements and consistently delivers exceptional service. Their team is professional, thorough, and reliable.'
  },
  {
    id: 3,
    name: '<PERSON>',
    position: 'Store Manager, Luxury Retail',
    image: 'https://randomuser.me/api/portraits/women/68.jpg',
    quote: 'Our high-end retail space requires immaculate presentation. CCWEB has exceeded our expectations with their premium cleaning services. Our customers notice the difference.'
  },
  {
    id: 4,
    name: '<PERSON>',
    position: 'Principal, Wilson Academy',
    image: 'https://randomuser.me/api/portraits/men/75.jpg',
    quote: 'We switched to CCWEB for our school cleaning needs last year, and the improvement has been remarkable. Their eco-friendly approach aligns with our values, and the quality is outstanding.'
  }
]);

const currentIndex = ref(0);
const autoplayInterval = ref(null);

const nextTestimonial = () => {
  currentIndex.value = (currentIndex.value + 1) % testimonials.value.length;
  animateTestimonial();
};

const prevTestimonial = () => {
  currentIndex.value = (currentIndex.value - 1 + testimonials.value.length) % testimonials.value.length;
  animateTestimonial();
};

const setTestimonial = (index) => {
  currentIndex.value = index;
  animateTestimonial();
  resetAutoplay();
};

const animateTestimonial = () => {
  gsap.fromTo('.testimonial-content', 
    { opacity: 0, y: 20 },
    { opacity: 1, y: 0, duration: 0.5, ease: 'power2.out' }
  );
};

const startAutoplay = () => {
  autoplayInterval.value = setInterval(() => {
    nextTestimonial();
  }, 5000);
};

const resetAutoplay = () => {
  clearInterval(autoplayInterval.value);
  startAutoplay();
};

onMounted(() => {
  startAutoplay();
  
  // Animate testimonials section
  gsap.from('.testimonials-header', {
    scrollTrigger: {
      trigger: '.testimonials',
      start: 'top 80%',
    },
    opacity: 0,
    y: 30,
    duration: 0.8,
    ease: 'power2.out'
  });
  
  gsap.from('.testimonial-slider', {
    scrollTrigger: {
      trigger: '.testimonials',
      start: 'top 70%',
    },
    opacity: 0,
    y: 50,
    duration: 1,
    ease: 'power2.out',
    delay: 0.3
  });
});
</script>

<template>
  <section id="testimonials" class="section testimonials">
    <div class="container">
      <div class="testimonials-header text-center">
        <h2>What Our Clients Say</h2>
      </div>

      <div class="testimonial-slider">
        <div class="testimonial-content">
          <div class="testimonial-quote">
            <p>{{ testimonials[currentIndex].quote }}</p>
          </div>

          <div class="testimonial-author">
            <div class="author-image">
              <img :src="testimonials[currentIndex].image" :alt="testimonials[currentIndex].name">
            </div>
            <div class="author-info">
              <h4>{{ testimonials[currentIndex].name }}</h4>
              <p>{{ testimonials[currentIndex].position }}</p>
            </div>
          </div>
        </div>

        <div class="testimonial-controls">
          <button class="control-btn prev" @click="prevTestimonial" aria-label="Previous testimonial">
            &#8592;
          </button>

          <div class="testimonial-dots">
            <button
              v-for="(testimonial, index) in testimonials"
              :key="testimonial.id"
              :class="['dot', { active: index === currentIndex }]"
              @click="setTestimonial(index)"
              :aria-label="`Testimonial ${index + 1}`"
            ></button>
          </div>

          <button class="control-btn next" @click="nextTestimonial" aria-label="Next testimonial">
            &#8594;
          </button>
        </div>
      </div>

      <div class="testimonials-cta text-center">
        <a href="#contact" class="btn btn-primary btn-sm">Get Started Today</a>
      </div>
    </div>
  </section>
</template>

<style scoped>
.testimonials {
  background-color: var(--off-white);
  position: relative;
  padding: 4rem 0;
  overflow: visible;
  min-height: 400px;
}

.testimonials-header {
  margin-bottom: 2rem;
}

.testimonial-slider {
  max-width: 700px;
  margin: 0 auto 2rem;
  background-color: var(--white);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  position: relative;
  opacity: 1;
  visibility: visible;
}

.testimonial-content {
  margin-bottom: 1.5rem;
}

.testimonial-quote {
  position: relative;
  margin-bottom: 1.5rem;
}

.testimonial-quote p {
  font-size: 1rem;
  line-height: 1.6;
  color: var(--text-dark);
  position: relative;
  z-index: 1;
  font-style: italic;
}

.testimonial-quote p::before {
  content: '"';
  font-size: 1.5rem;
  color: var(--primary-light);
  font-family: var(--font-heading);
  margin-right: 0.2rem;
}

.testimonial-quote p::after {
  content: '"';
  font-size: 1.5rem;
  color: var(--primary-light);
  font-family: var(--font-heading);
  margin-left: 0.2rem;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.author-image {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid var(--primary-light);
}

.author-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.author-info h4 {
  margin-bottom: 0.1rem;
  color: var(--primary-dark);
  font-size: 1rem;
}

.author-info p {
  color: var(--text-muted);
  font-size: 0.8rem;
  margin-bottom: 0;
}

.testimonial-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.8rem;
}

.control-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: var(--primary-dark);
  cursor: pointer;
  transition: transform 0.3s ease;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn:hover {
  transform: scale(1.2);
}

.testimonial-dots {
  display: flex;
  gap: 0.4rem;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--primary-light);
  opacity: 0.3;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot.active {
  opacity: 1;
  transform: scale(1.3);
}

.testimonials-cta {
  text-align: center;
  margin-top: 1.5rem;
}

.btn-sm {
  padding: 0.5rem 1.5rem;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .testimonial-slider {
    padding: 1.2rem;
  }
  
  .testimonial-quote p {
    font-size: 0.9rem;
  }
}
</style>