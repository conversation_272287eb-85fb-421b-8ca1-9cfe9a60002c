<script setup>
import { ref, onMounted } from 'vue';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const statsRef = ref(null);

onMounted(() => {
  if (statsRef.value) {
    // Set initial state
    gsap.set(statsRef.value, {
      opacity: 0,
      y: 50
    });

    // Set initial state for stat items
    gsap.set('.stat-item', {
      opacity: 0,
      y: 30,
      scale: 0.8
    });

    // Create entrance animation
    ScrollTrigger.create({
      trigger: statsRef.value,
      start: "top 80%",
      onEnter: () => {
        // Animate container first
        gsap.to(statsRef.value, {
          opacity: 1,
          y: 0,
          duration: 0.4,
          ease: "power2.out"
        });

        // Then animate stat items with stagger
        gsap.to('.stat-item', {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.3,
          stagger: 0.03,
          ease: "back.out(1.7)",
          delay: 0.05
        });

        // Animate the numbers counting up
        const statItems = document.querySelectorAll('.stat-item');
        statItems.forEach((item, index) => {
          const numberElement = item.querySelector('.stat-number');
          const endValue = numberElement.textContent;
          
          // Extract the numeric value and suffix
          let numericValue = 0;
          let suffix = '';
          
          if (endValue.includes('+')) {
            numericValue = parseInt(endValue.replace('+', ''));
            suffix = '+';
          } else if (endValue.includes('%')) {
            numericValue = parseInt(endValue.replace('%', ''));
            suffix = '%';
          } else if (endValue.includes('/')) {
            numericValue = parseInt(endValue.split('/')[0]);
            suffix = '/7';
          } else {
            numericValue = parseInt(endValue);
          }
          
          // Set initial value to 0
          numberElement.textContent = '0' + suffix;
          
          // Number counter animation
          const counterObj = { value: 0 };
          gsap.to(counterObj, {
            value: numericValue,
            duration: 0.4,
            ease: "power2.out",
            delay: 0.1 + (index * 0.02),
            onUpdate: function() {
              const currentValue = Math.floor(counterObj.value);
              numberElement.textContent = currentValue + suffix;
            },
            onComplete: function() {
              // Ensure final value is exact
              numberElement.textContent = endValue;
            }
          });
        });
      }
    });
  }
});
</script>

<template>
  <section class="stats-section" ref="statsRef">
    <div class="container">
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-number">5000+</div>
          <div class="stat-label">Happy Clients</div>
          <div class="stat-glow"></div>
        </div>
        <div class="stat-item">
          <div class="stat-number">99%</div>
          <div class="stat-label">Satisfaction Rate</div>
          <div class="stat-glow"></div>
        </div>
        <div class="stat-item">
          <div class="stat-number">10+</div>
          <div class="stat-label">Years Experience</div>
          <div class="stat-glow"></div>
        </div>
        <div class="stat-item">
          <div class="stat-number">24/7</div>
          <div class="stat-label">Customer Support</div>
          <div class="stat-glow"></div>
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped>
.stats-section {
  background: var(--color-primary);
  padding: 4rem 0;
  position: relative;
  overflow: hidden;
}

.stats-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  position: relative;
  z-index: 2;
}

.stat-item {
  text-align: center;
  padding: 2rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-item:hover {
  transform: translateY(-10px);
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.stat-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(252, 203, 1, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  opacity: 0;
  transition: all 0.3s ease;
  pointer-events: none;
}

.stat-item:hover .stat-glow {
  opacity: 1;
  animation: pulse 2s ease-in-out infinite;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--color-secondary);
  margin-bottom: 0.5rem;
  will-change: transform;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
  text-shadow: 0 2px 10px rgba(252, 203, 1, 0.3);
}

.stat-item:hover .stat-number {
  transform: scale(1.05);
  text-shadow: 0 4px 20px rgba(252, 203, 1, 0.6);
}

.stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: relative;
  z-index: 2;
}

@keyframes pulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.3;
  }
}

@media (max-width: 992px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .stat-number {
    font-size: 2rem;
  }
}
</style>
