<script setup>
import { onMounted } from 'vue';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

onMounted(() => {
  // Animate about section elements on scroll
  gsap.from('.about-image', {
    scrollTrigger: {
      trigger: '.about',
      start: 'top 70%',
    },
    opacity: 0,
    x: -50,
    duration: 1,
    ease: 'power2.out'
  });
  
  gsap.from('.about-content', {
    scrollTrigger: {
      trigger: '.about',
      start: 'top 70%',
    },
    opacity: 0,
    x: 50,
    duration: 1,
    ease: 'power2.out'
  });
  
  // Set initial state for features
  gsap.set('.feature-item', {
    opacity: 0,
    y: 30
  });

  // Animate features on scroll
  ScrollTrigger.create({
    trigger: '.features',
    start: 'top 80%',
    onEnter: () => {
      gsap.to('.feature-item', {
        opacity: 1,
        y: 0,
        stagger: 0.15,
        duration: 0.8,
        ease: 'power2.out'
      });
    }
  });
});
</script>

<template>
  <section id="about" class="section about">
    <div class="container">
      <div class="about-grid">
        <div class="about-image">
          <img src="/aboutccwebcleaning.jpg" alt="Professional cleaning team">
          <div class="experience-badge">
            <span class="years">10+</span>
            <span class="text">Years of Excellence</span>
          </div>
        </div>
        
        <div class="about-content">
          <div class="section-header">
            <h2>About Clear Choice Cleaners</h2>
            <p class="section-subtitle">
              Setting the standard for commercial cleaning excellence
            </p>
          </div>
          
          <p>
            At Clear Choice Cleaners, we understand that a clean environment is essential for business success. For over a decade, we've been providing premium commercial cleaning services to businesses of all sizes, helping them maintain immaculate spaces that impress clients and provide healthy environments for employees.
          </p>
          
          <p>
            Our team of highly trained professionals uses state-of-the-art equipment and eco-friendly cleaning products to deliver exceptional results while minimizing environmental impact. We take pride in our attention to detail and commitment to excellence in every project we undertake.
          </p>
          
          <div class="about-cta">
            <a href="#contact" class="btn btn-primary">Get in Touch</a>
          </div>
        </div>
      </div>
      
      <div class="features">
        <div class="feature-item">
          <div class="feature-icon">✓</div>
          <div class="feature-content">
            <h3>Certified Professionals</h3>
            <p>Our cleaning specialists undergo rigorous training and certification to ensure the highest standards.</p>
          </div>
        </div>
        
        <div class="feature-item">
          <div class="feature-icon">✓</div>
          <div class="feature-content">
            <h3>Eco-Friendly Approach</h3>
            <p>We use environmentally responsible cleaning products and methods to protect both your space and the planet.</p>
          </div>
        </div>
        
        <div class="feature-item">
          <div class="feature-icon">✓</div>
          <div class="feature-content">
            <h3>Customized Solutions</h3>
            <p>Every business is unique, which is why we tailor our cleaning services to meet your specific needs.</p>
          </div>
        </div>
        
        <div class="feature-item">
          <div class="feature-icon">✓</div>
          <div class="feature-content">
            <h3>Reliability & Consistency</h3>
            <p>Count on us to deliver consistent, high-quality cleaning services on schedule, every time.</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped>
.about {
  background-color: var(--white);
}

.about-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  margin-bottom: 5rem;
}

.about-image {
  position: relative;
}

.about-image img {
  width: 100%;
  height: auto;
  border-radius: 10px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.experience-badge {
  position: absolute;
  bottom: -30px;
  right: -30px;
  background-color: var(--primary-dark);
  color: var(--white);
  padding: 1.5rem;
  border-radius: 50%;
  width: 120px;
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.experience-badge .years {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
  font-family: var(--font-heading);
}

.experience-badge .text {
  font-size: 0.9rem;
  margin-top: 0.3rem;
}

.about-content .section-header {
  text-align: left;
  margin-bottom: 2rem;
}

.about-content p {
  margin-bottom: 1.5rem;
}

.about-cta {
  margin-top: 2rem;
}

.features {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  margin-top: 4rem;
  padding: 2rem 0;
  position: relative;
  z-index: 1;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.feature-icon {
  background-color: var(--primary-light);
  color: var(--primary-dark);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.2rem;
  font-weight: 700;
  flex-shrink: 0;
}

.feature-content h3 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: var(--primary-dark);
}

.feature-content p {
  color: var(--text-muted);
  font-size: 0.95rem;
  margin-bottom: 0;
}

@media (max-width: 992px) {
  .about-grid {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
  
  .experience-badge {
    bottom: -20px;
    right: -20px;
    width: 100px;
    height: 100px;
  }
  
  .features {
    grid-template-columns: 1fr;
  }
}
</style>