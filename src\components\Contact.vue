<script setup>
import { ref, onMounted } from 'vue';
import { gsap } from 'gsap';

const formData = ref({
  name: '',
  email: '',
  phone: '',
  company: '',
  message: '',
  service: 'Office Cleaning'
});

const isSubmitting = ref(false);
const isSubmitted = ref(false);
const errorMessage = ref('');

const services = ref([
  'Office Cleaning',
  'Retail Spaces',
  'Medical Facilities',
  'Educational Institutions',
  'Hospitality',
  'Industrial Cleaning',
  'Other'
]);

const submitForm = () => {
  isSubmitting.value = true;
  errorMessage.value = '';
  
  // Simulate form submission
  setTimeout(() => {
    isSubmitting.value = false;
    isSubmitted.value = true;
    
    // Reset form after submission
    formData.value = {
      name: '',
      email: '',
      phone: '',
      company: '',
      message: '',
      service: 'Office Cleaning'
    };
    
    // Show success message for 5 seconds
    setTimeout(() => {
      isSubmitted.value = false;
    }, 5000);
  }, 1500);
};

onMounted(() => {
  // Animate contact section
  gsap.from('.contact-header', {
    scrollTrigger: {
      trigger: '.contact',
      start: 'top 80%',
    },
    opacity: 0,
    y: 30,
    duration: 0.8,
    ease: 'power2.out'
  });
  
  gsap.from('.contact-form', {
    scrollTrigger: {
      trigger: '.contact-form',
      start: 'top 80%',
    },
    opacity: 0,
    y: 30,
    duration: 0.8,
    ease: 'power2.out',
    delay: 0.2
  });
  
  gsap.from('.contact-info', {
    scrollTrigger: {
      trigger: '.contact-info',
      start: 'top 80%',
    },
    opacity: 0,
    x: 30,
    duration: 0.8,
    ease: 'power2.out',
    delay: 0.4
  });
});
</script>

<template>
  <section id="contact" class="section contact">
    <div class="container">
      <div class="contact-header text-center">
        <h2>Get in Touch</h2>
      </div>
      
      <div class="contact-grid">
        <div class="contact-form-container">
          <form class="contact-form" @submit.prevent="submitForm">
            <div class="form-success fade-in-up" v-if="isSubmitted">
              <div class="success-icon pulse-animation">✓</div>
              <h3>Thank you for your message!</h3>
              <p>We'll get back to you as soon as possible.</p>
            </div>

            <div v-else>
              <div class="form-row stagger-item">
                <div class="form-group">
                  <label for="name">Full Name</label>
                  <input
                    type="text"
                    id="name"
                    v-model="formData.name"
                    required
                    placeholder="Your name"
                    class="glow-on-hover"
                  >
                </div>

                <div class="form-group">
                  <label for="email">Email Address</label>
                  <input
                    type="email"
                    id="email"
                    v-model="formData.email"
                    required
                    placeholder="Your email"
                    class="glow-on-hover"
                  >
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label for="phone">Phone Number</label>
                  <input
                    type="tel"
                    id="phone"
                    v-model="formData.phone"
                    placeholder="Your phone number"
                  >
                </div>
                
                <div class="form-group">
                  <label for="service">Service Interested In</label>
                  <select id="service" v-model="formData.service">
                    <option v-for="service in services" :key="service" :value="service">
                      {{ service }}
                    </option>
                  </select>
                </div>
              </div>
              
              <div class="form-group">
                <label for="message">Message</label>
                <textarea
                  id="message"
                  v-model="formData.message"
                  rows="3"
                  placeholder="Tell us about your cleaning needs"
                ></textarea>
              </div>
              
              <div class="form-error" v-if="errorMessage">
                {{ errorMessage }}
              </div>
              
              <button
                type="submit"
                class="btn btn-primary submit-btn"
                :disabled="isSubmitting"
              >
                <span v-if="isSubmitting">Sending...</span>
                <span v-else>Send Message</span>
              </button>
            </div>
          </form>
        </div>
        
        <div class="contact-info">
          <div class="info-card">
            <h3>Contact Us</h3>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-icon">📞</div>
                <div class="info-content">
                  <p>+****************</p>
                </div>
              </div>

              <div class="info-item">
                <div class="info-icon">✉️</div>
                <div class="info-content">
                  <p><EMAIL></p>
                </div>
              </div>

              <div class="info-item">
                <div class="info-icon">🏢</div>
                <div class="info-content">
                  <p>40 Hopewell Way Unit #10<br>Calgary, AB, T3J 5H7</p>
                </div>
              </div>
              
              <div class="info-item">
                <div class="info-icon">⏰</div>
                <div class="info-content">
                  <p>Mon-Fri: 8AM-6PM<br>Sat: 9AM-2PM</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped>
.contact {
  background-color: var(--white);
  position: relative;
  padding: 3rem 0;
}

.contact-header {
  margin-bottom: 2rem;
}

.contact-grid {
  display: grid;
  grid-template-columns: 3fr 2fr;
  gap: 1.5rem;
}

.contact-form-container {
  background-color: var(--off-white);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.contact-form {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

label {
  display: block;
  margin-bottom: 0.3rem;
  font-weight: 500;
  color: var(--primary-dark);
  font-size: 0.9rem;
}

input, select, textarea {
  width: 100%;
  padding: 0.6rem 0.8rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-family: var(--font-body);
  font-size: 0.9rem;
  transition: border-color 0.3s ease;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--primary-dark);
}

.submit-btn {
  width: 100%;
  padding: 0.8rem;
  font-size: 1rem;
  margin-top: 0.8rem;
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.form-error {
  color: #e53935;
  margin-bottom: 0.8rem;
  font-size: 0.8rem;
}

.form-success {
  text-align: center;
  padding: 1.5rem 0;
}

.success-icon {
  background-color: #4caf50;
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5rem;
  margin: 0 auto 1rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
}

.info-card {
  background-color: var(--primary-dark);
  color: var(--white);
  border-radius: 8px;
  padding: 1.5rem;
  height: 100%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.info-card h3 {
  color: var(--primary-light);
  margin-bottom: 1rem;
  font-size: 1.4rem;
  text-align: center;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.info-item {
  display: flex;
  gap: 0.5rem;
  align-items: flex-start;
}

.info-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.info-content p {
  color: var(--off-white);
  line-height: 1.4;
  margin-bottom: 0;
  font-size: 0.85rem;
}

@media (max-width: 992px) {
  .contact-grid {
    grid-template-columns: 1fr;
  }
  
  .info-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
    gap: 0;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}
</style>