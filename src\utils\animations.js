// Animation utilities for scroll-triggered animations

/**
 * Initialize intersection observer for scroll animations
 */
export function initScrollAnimations() {
  // Create intersection observer
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('in-view');
        }
      });
    },
    {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    }
  );

  // Observe all elements with animation classes
  const animatedElements = document.querySelectorAll(
    '.text-reveal, .fade-in-up, .fade-in-left, .fade-in-right, .stagger-item'
  );

  animatedElements.forEach((el) => {
    observer.observe(el);
  });

  return observer;
}

/**
 * Add stagger animation to a container's children
 */
export function addStaggerAnimation(container, delay = 100) {
  if (!container) return;

  const children = container.children;
  Array.from(children).forEach((child, index) => {
    child.classList.add('stagger-item');
    child.style.transitionDelay = `${index * delay}ms`;
  });
}

/**
 * Add floating animation to elements
 */
export function addFloatingAnimation(elements) {
  if (!elements) return;

  elements.forEach((element, index) => {
    element.classList.add('float-animation');
    element.style.animationDelay = `${index * 0.5}s`;
  });
}

/**
 * Create parallax effect for elements
 */
export function initParallax() {
  const parallaxElements = document.querySelectorAll('[data-parallax]');
  
  if (parallaxElements.length === 0) return;

  const handleScroll = () => {
    const scrolled = window.pageYOffset;
    
    parallaxElements.forEach((element) => {
      const rate = scrolled * (element.dataset.parallax || 0.5);
      element.style.transform = `translateY(${rate}px)`;
    });
  };

  window.addEventListener('scroll', handleScroll, { passive: true });
  
  return () => {
    window.removeEventListener('scroll', handleScroll);
  };
}

/**
 * Add hover tilt effect to cards
 */
export function addTiltEffect(elements) {
  if (!elements) return;

  elements.forEach((element) => {
    element.addEventListener('mousemove', (e) => {
      const rect = element.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      
      const rotateX = (y - centerY) / 10;
      const rotateY = (centerX - x) / 10;
      
      element.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale3d(1.02, 1.02, 1.02)`;
    });

    element.addEventListener('mouseleave', () => {
      element.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale3d(1, 1, 1)';
    });
  });
}

/**
 * Create magnetic effect for buttons
 */
export function addMagneticEffect(elements) {
  if (!elements) return;

  elements.forEach((element) => {
    element.addEventListener('mousemove', (e) => {
      const rect = element.getBoundingClientRect();
      const x = e.clientX - rect.left - rect.width / 2;
      const y = e.clientY - rect.top - rect.height / 2;
      
      element.style.transform = `translate(${x * 0.1}px, ${y * 0.1}px)`;
    });

    element.addEventListener('mouseleave', () => {
      element.style.transform = 'translate(0px, 0px)';
    });
  });
}

/**
 * Add smooth reveal animation to text
 */
export function addTextReveal(element, delay = 0) {
  if (!element) return;

  const text = element.textContent;
  const words = text.split(' ');
  
  element.innerHTML = words
    .map((word, index) => 
      `<span class="word" style="display: inline-block; opacity: 0; transform: translateY(20px); transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) ${delay + index * 0.1}s;">${word}</span>`
    )
    .join(' ');

  // Trigger animation
  setTimeout(() => {
    const wordElements = element.querySelectorAll('.word');
    wordElements.forEach((word) => {
      word.style.opacity = '1';
      word.style.transform = 'translateY(0px)';
    });
  }, 100);
}

/**
 * Add morphing background effect
 */
export function addMorphingBackground(element) {
  if (!element) return;

  const colors = [
    '#FFFF00',
      '#FFD700',
      '#000000'
  ];

  let currentIndex = 0;

  setInterval(() => {
    currentIndex = (currentIndex + 1) % colors.length;
    element.style.background = colors[currentIndex];
  }, 5000);
}

/**
 * Add ripple effect to buttons
 */
export function addRippleEffect(elements) {
  if (!elements) return;

  elements.forEach((element) => {
    element.addEventListener('click', (e) => {
      const ripple = document.createElement('span');
      const rect = element.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      const x = e.clientX - rect.left - size / 2;
      const y = e.clientY - rect.top - size / 2;
      
      ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: scale(0);
        animation: ripple 0.6s ease-out;
        pointer-events: none;
      `;
      
      element.style.position = 'relative';
      element.style.overflow = 'hidden';
      element.appendChild(ripple);
      
      setTimeout(() => {
        ripple.remove();
      }, 600);
    });
  });

  // Add ripple animation CSS
  if (!document.querySelector('#ripple-styles')) {
    const style = document.createElement('style');
    style.id = 'ripple-styles';
    style.textContent = `
      @keyframes ripple {
        to {
          transform: scale(2);
          opacity: 0;
        }
      }
    `;
    document.head.appendChild(style);
  }
}

/**
 * Initialize all animations
 */
export function initAllAnimations() {
  // Initialize scroll animations
  const scrollObserver = initScrollAnimations();
  
  // Initialize parallax
  const parallaxCleanup = initParallax();
  
  // Add effects to common elements
  const cards = document.querySelectorAll('.card, .service-card, .testimonial-card');
  addTiltEffect(cards);
  
  const buttons = document.querySelectorAll('.btn');
  addMagneticEffect(buttons);
  addRippleEffect(buttons);
  
  const floatingElements = document.querySelectorAll('.float-animation');
  addFloatingAnimation(floatingElements);
  
  // Return cleanup function
  return () => {
    if (scrollObserver) scrollObserver.disconnect();
    if (parallaxCleanup) parallaxCleanup();
  };
}
