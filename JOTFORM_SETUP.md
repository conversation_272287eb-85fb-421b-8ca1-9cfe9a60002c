# JotForm Integration Setup Guide

This guide will help you set up JotForm integration for the Quick Quote form on your website.

## Step 1: Create a JotForm Account and Form

1. Go to [JotForm.com](https://www.jotform.com/) and create an account (if you don't have one)
2. Click "Create Form" and choose "Start from Scratch"
3. Add the following fields to your form:

### Required Fields:
- **Name** (Text Input)
  - Field Type: Text
  - Label: "Your Name"
  - Make it required

- **Phone** (Phone Input)
  - Field Type: Phone
  - Label: "Phone Number" 
  - Make it required

- **Email** (Email Input)
  - Field Type: Email
  - Label: "Email Address"
  - Make it required

- **Service Type** (Dropdown)
  - Field Type: Dropdown
  - Label: "Service Type"
  - Options:
    - Office Cleaning
    - Retail Spaces
    - Medical Facilities
    - Educational Institutions
    - Hospitality
    - Industrial Cleaning

- **Square Footage** (Number Input)
  - Field Type: Number
  - Label: "Square Footage"
  - Make it optional

## Step 2: Get Your Form ID

1. After creating your form, look at the URL in your browser
2. The URL will look like: `https://form.jotform.com/*********`
3. The number at the end (*********) is your Form ID
4. Copy this number

## Step 3: Get Field Names

1. In your JotForm dashboard, click on your form
2. Click "Settings" → "Form Source Code"
3. Look for input field names like:
   - `q3_name` (for name field)
   - `q4_phone` (for phone field)
   - `q5_email` (for email field)
   - `q6_serviceType` (for service type dropdown)
   - `q7_squareFootage` (for square footage field)

## Step 4: Update the Website Code

1. Open `src/components/QuickQuoteForm.vue`
2. Replace `YOUR_JOTFORM_ID` with your actual Form ID
3. Update the field names in the `formData.append()` calls:

```javascript
// Replace these with your actual field names from Step 3
formData.append('q3_name', quickQuoteData.value.name);
formData.append('q4_phone', quickQuoteData.value.phone);
formData.append('q5_email', quickQuoteData.value.email);
formData.append('q6_serviceType', quickQuoteData.value.serviceType);
formData.append('q7_squareFootage', quickQuoteData.value.squareFootage);
```

## Step 5: Set Up Email Notifications

1. In your JotForm dashboard, go to Settings → Emails
2. Click "Add Email" to create a notification email
3. Set up:
   - **Send To**: Your email address
   - **Subject**: "New Quote Request from Website"
   - **Email Content**: Include all form fields

## Step 6: Test the Integration

1. Save your changes and refresh your website
2. Fill out the quote form and submit it
3. Check your email for the notification
4. Verify the submission appears in your JotForm dashboard

## Troubleshooting

### Form not submitting?
- Double-check your Form ID is correct
- Verify field names match exactly (case-sensitive)
- Check browser console for error messages

### Not receiving emails?
- Check your JotForm email notification settings
- Verify your email address is correct
- Check spam/junk folder

### Need help?
- JotForm has excellent documentation at [api.jotform.com](https://api.jotform.com/docs/)
- Contact JotForm support if you have issues with their platform

## Security Note

The form uses `no-cors` mode for submission, which is required by JotForm but means we can't read the response. This is normal and secure - JotForm will still receive and process the submissions.
