<script setup>
import { ref, onMounted } from 'vue';
import { gsap } from 'gsap';
import QuickQuoteForm from './QuickQuoteForm.vue';

// Simple text splitter function since SplitText might not be available
const splitText = (element, type) => {
  if (!element) return [];
  
  const text = element.textContent;
  const chars = [];
  
  // Clear the element
  element.textContent = '';
  
  // Create spans for each character
  for (let i = 0; i < text.length; i++) {
    const char = document.createElement('span');
    char.style.display = 'inline-block';
    char.style.willChange = 'transform, opacity';
    char.textContent = text[i] === ' ' ? '\u00A0' : text[i]; // Use non-breaking space for spaces
    element.appendChild(char);
    chars.push(char);
  }
  
  return chars;
};

const heroRef = ref(null);
const headingRef = ref(null);
const priceRef = ref(null);
const quoteFormRef = ref(null);

// Scroll to services section
const scrollToServices = () => {
  const element = document.getElementById('services');
  if (element) {
    window.scrollTo({
      top: element.offsetTop - 80,
      behavior: 'smooth'
    });
  }
};

// Remove features data since we're replacing with quote form

onMounted(() => {
  try {
    // Create a master timeline for hero animations with faster overall duration
    const masterTl = gsap.timeline({
      defaults: {
        ease: "power4.out",
        duration: 0.8 // Reduced from 1.2
      }
    });
    
    // Create parallel animation groups
    
    // Group 1: Background elements (run in parallel)
    const bgTl = gsap.timeline();
    
    // Background video and overlay (in parallel)
    bgTl.fromTo('.hero-video-bg',
      {
        scale: 1.1,
        opacity: 0,
        filter: "blur(10px)"
      },
      {
        scale: 1,
        opacity: 1,
        filter: "blur(0px)",
        duration: 1.2, // Reduced from 2
        ease: "power2.inOut"
      }
    );
    
    bgTl.fromTo('.hero-overlay',
      {
        opacity: 0,
        background: "rgba(0, 0, 0, 0)"
      },
      {
        opacity: 1,
        background: "rgba(0, 0, 0, 0.8)",
        duration: 1.2, // Reduced from 1.8
        ease: "power2.inOut"
      },
      "<" // Start at the same time as previous animation
    );
    
    // Add background timeline to master
    masterTl.add(bgTl);
    
    // Group 2: Text elements (start slightly after backgrounds begin)
    const textTl = gsap.timeline();
    
    // Heading words animation
    const headingWords = headingRef.value ? headingRef.value.querySelectorAll('.heading-word') : [];
    if (headingWords.length > 0) {
      textTl.fromTo(headingWords,
        {
          y: 100,
          opacity: 0,
          rotationX: -30,
          transformOrigin: "0% 50% -50",
          filter: "blur(10px)"
        },
        {
          y: 0,
          opacity: 1,
          rotationX: 0,
          filter: "blur(0px)",
          stagger: 0.08, // Reduced from 0.15
          duration: 0.8,
          ease: "back.out(1.7)"
        }
      );
    }
    
    // Heading accent animation (in parallel with words)
    const headingAccent = document.querySelector('.heading-accent::after');
    if (headingAccent) {
      textTl.fromTo(headingAccent,
        {
          width: "0%",
          opacity: 0,
          x: -20
        },
        {
          width: "100%",
          opacity: 0.6,
          x: 0,
          duration: 0.6, // Reduced from 0.8
          ease: "power2.inOut"
        },
        "<0.2" // Start slightly after heading words begin
      );
    }
    


    // Price section animation (start with heading)
    if (priceRef.value) {
      textTl.fromTo(priceRef.value,
        {
          y: 50,
          opacity: 0,
          scale: 0.9
        },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 0.8,
          ease: "power3.out"
        },
        "<0.3"
      );
    }
    
    // Add text timeline to master (start slightly after background begins)
    masterTl.add(textTl, "-=1");
    
    // Group 3: Interactive elements (CTA button and quote form)
    const interactiveTl = gsap.timeline();

    // CTA button animation
    const ctaButton = document.querySelector('.hero-cta .cta-button');
    if (ctaButton) {
      interactiveTl.fromTo(ctaButton,
        {
          y: 40,
          opacity: 0,
          scale: 0.8,
          filter: "blur(5px)"
        },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          filter: "blur(0px)",
          duration: 0.7,
          ease: "elastic.out(1, 0.5)"
        }
      );
    }

    // Quote form animation (in parallel with CTA)
    if (quoteFormRef.value) {
      interactiveTl.fromTo(quoteFormRef.value,
        {
          x: 100,
          opacity: 0,
          rotationY: -15,
          transformOrigin: "left center",
          filter: "blur(10px)"
        },
        {
          x: 0,
          opacity: 1,
          rotationY: 0,
          filter: "blur(0px)",
          duration: 0.8,
          ease: "power3.out"
        },
        "<0.1" // Start slightly after CTA begins
      );
    }
    
    // Add interactive elements timeline to master (start with text)
    masterTl.add(interactiveTl, "-=0.8");
    

    
    // Set the total duration to approximately 2.5 seconds
    masterTl.totalDuration(2.5);
  } catch (error) {
    console.error("Error in hero animations:", error);
  }
});
</script>

<template>
  <section id="home" class="hero-section" ref="heroRef">
    <!-- Video background with parallax effect -->
    <video autoplay loop muted playsinline class="hero-video-bg">
      <source src="/ClearChoiceHero.mp4" type="video/mp4">
    </video>

    <!-- Content overlay with gradient -->
    <div class="hero-overlay"></div>

    <!-- Floating elements for depth -->
    <div class="floating-elements">
      <div class="floating-orb floating-orb-1"></div>
      <div class="floating-orb floating-orb-2"></div>
      <div class="floating-orb floating-orb-3"></div>
    </div>

    <!-- Main content -->
    <div class="hero-container">
      <!-- Left side: Hero Text -->
      <div class="hero-content">
        <h1 class="hero-title" ref="headingRef">
          <span class="title-line-1">MONTHLY CLEANING</span>
          <span class="title-line-2">PACKAGES</span>
          <span class="title-line-3">START AT</span>
          <span class="title-price">99</span>
        </h1>
      </div>

      <!-- Right side: Quote Form -->
      <div class="quote-form-container" ref="quoteFormRef">
        <QuickQuoteForm />
      </div>
    </div>


  </section>
</template>

<style scoped>
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  padding-top: 80px;
  perspective: 1000px;
  will-change: transform;
}

.hero-video-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
  transform-origin: center center;
  will-change: transform, filter;
  transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  opacity: 0.7;
}

.hero-section:hover .hero-video-bg {
  transform: scale(1.05);
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.85);
  z-index: 1;
  will-change: opacity, background;
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.hero-section:hover .hero-overlay {
  background: rgba(0, 0, 0, 0.8);
}

/* Floating elements for depth */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.floating-orb {
  position: absolute;
  border-radius: 50%;
  background: rgba(252, 203, 1, 0.1);
  backdrop-filter: blur(20px);
  animation: float 6s ease-in-out infinite;
}

.floating-orb-1 {
  width: 200px;
  height: 200px;
  top: 20%;
  right: 10%;
  animation-delay: 0s;
}

.floating-orb-2 {
  width: 150px;
  height: 150px;
  bottom: 30%;
  left: 5%;
  animation-delay: 2s;
}

.floating-orb-3 {
  width: 100px;
  height: 100px;
  top: 60%;
  right: 30%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.6;
  }
}

.hero-container {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 3rem 4rem;
  max-width: 1800px;
  margin: 0 auto;
  min-height: calc(100vh - 80px);
  gap: 4rem;
}

.hero-content {
  flex: 1;
  text-align: left;
  max-width: 50%;
}

.hero-title {
  font-family: 'Bricolage Grotesque', 'Arial', sans-serif !important;
  font-weight: 800 !important;
  font-style: normal !important;
  line-height: 1.1;
  color: #FFFFFF;
  text-transform: uppercase;
  letter-spacing: 0.02em;
  margin: 0;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.8);
}

.title-line-1,
.title-line-2,
.title-line-3 {
  display: block;
  font-size: clamp(2.5rem, 5vw, 4.5rem);
  margin-bottom: 0.2rem;
  color: #FFFFFF;
}

.title-price {
  display: block;
  font-size: clamp(6rem, 12vw, 12rem);
  font-weight: 900 !important;
  color: var(--color-teal-light);
  text-shadow: 3px 3px 12px rgba(0, 0, 0, 0.9);
  margin-top: 0.5rem;
}









/* Removed arrow pulse animation */

/* Quote Form Container Styles */
.quote-form-container {
  position: relative;
  flex: 1;
  max-width: 45%;
  will-change: transform;
  transition: all 0.3s ease;
  z-index: 3;
}

.quote-form-container:hover {
  transform: translateY(-5px);
}

/* Removed old feature box styles - replaced with quote form */

/* Removed sparkle styles */






/* Responsive styles */
@media (max-width: 992px) {
  .hero-container {
    flex-direction: column;
    padding: 2rem;
    gap: 2rem;
    text-align: center;
    min-height: calc(100vh - 80px);
  }

  .hero-content {
    max-width: 100%;
    text-align: center;
  }

  .title-line-1,
  .title-line-2,
  .title-line-3 {
    font-size: clamp(2rem, 6vw, 3.5rem);
  }

  .title-price {
    font-size: clamp(4rem, 10vw, 8rem);
  }

  .quote-form-container {
    max-width: 100%;
  }

  .floating-orb {
    display: none;
  }
}

@media (max-width: 768px) {
  .title-line-1,
  .title-line-2,
  .title-line-3 {
    font-size: clamp(1.8rem, 5vw, 2.8rem);
  }

  .title-price {
    font-size: clamp(3rem, 8vw, 6rem);
  }

  .hero-container {
    padding: 2rem 1rem;
  }
}

@media (max-width: 480px) {
  .title-line-1,
  .title-line-2,
  .title-line-3 {
    font-size: clamp(1.5rem, 4vw, 2.2rem);
  }

  .title-price {
    font-size: clamp(2.5rem, 7vw, 4.5rem);
  }

  .hero-container {
    padding: 1.5rem 1rem;
    gap: 1.5rem;
  }
}
</style>