/* Global animations and effects inspired by Oura design */

/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Global transitions for all elements */
* {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              box-shadow 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Enhanced button animations */
.btn {
  position: relative;
  overflow: hidden;
  transform: translateZ(0);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  transition: left 0.6s ease;
}

.btn:hover::before {
  left: 100%;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Card hover effects */
.card, .service-card, .testimonial-card {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.card::after, .service-card::after, .testimonial-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(252, 203, 1, 0.05);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

.card:hover, .service-card:hover, .testimonial-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.card:hover::after, .service-card:hover::after, .testimonial-card:hover::after {
  opacity: 1;
}

/* Text reveal animations */
.text-reveal {
  opacity: 1;
  transform: translateY(0px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.text-reveal.in-view {
  opacity: 1;
  transform: translateY(0);
}

/* Stagger animation for lists */
.stagger-item {
  opacity: 1;
  transform: translateY(0px);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.stagger-item.in-view {
  opacity: 1;
  transform: translateY(0);
}

.stagger-item:nth-child(1) { transition-delay: 0.1s; }
.stagger-item:nth-child(2) { transition-delay: 0.2s; }
.stagger-item:nth-child(3) { transition-delay: 0.3s; }
.stagger-item:nth-child(4) { transition-delay: 0.4s; }
.stagger-item:nth-child(5) { transition-delay: 0.5s; }
.stagger-item:nth-child(6) { transition-delay: 0.6s; }

/* Floating animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

.float-animation:nth-child(2n) {
  animation-delay: 1s;
}

.float-animation:nth-child(3n) {
  animation-delay: 2s;
}

/* Glow effects */
.glow-on-hover {
  position: relative;
  transition: all 0.3s ease;
}

.glow-on-hover::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: var(--color-secondary);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
  filter: blur(10px);
}

.glow-on-hover:hover::before {
  opacity: 0.7;
}

/* Pulse animation */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

.pulse-animation {
  animation: pulse 2s ease-in-out infinite;
}

/* Shimmer effect */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.shimmer {
  background: rgba(255, 255, 255, 0.4);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Scale on hover */
.scale-on-hover {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scale-on-hover:hover {
  transform: scale(1.05);
}

/* Fade in from bottom */
.fade-in-up {
  opacity: 1;
  transform: translateY(0px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.fade-in-up.in-view {
  opacity: 1;
  transform: translateY(0);
}

/* Fade in from left */
.fade-in-left {
  opacity: 1;
  transform: translateX(0px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.fade-in-left.in-view {
  opacity: 1;
  transform: translateX(0);
}

/* Fade in from right */
.fade-in-right {
  opacity: 1;
  transform: translateX(0px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.fade-in-right.in-view {
  opacity: 1;
  transform: translateX(0);
}

/* Rotate on hover */
.rotate-on-hover {
  transition: transform 0.3s ease;
}

.rotate-on-hover:hover {
  transform: rotate(5deg);
}

/* Backdrop blur effects */
.glass-effect {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Enhanced shadows */
.shadow-soft {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.shadow-medium {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.shadow-strong {
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

/* Gradient text */
.gradient-text {
  background: var(--color-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Loading animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.spin {
  animation: spin 1s linear infinite;
}

/* Bounce animation */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

.bounce {
  animation: bounce 1s ease infinite;
}
