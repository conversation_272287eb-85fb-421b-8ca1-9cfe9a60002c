<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { gsap } from 'gsap';

const isMenuOpen = ref(false);
const isScrolled = ref(false);
const activeSection = ref('home');
const navbarRef = ref(null);
const logoRef = ref(null);
const navItems = ref([]);

// Define navigation items
const navigationItems = [
  { id: 'home', label: 'Home' },
  { id: 'services', label: 'Services' },
  { id: 'about', label: 'About' },
  { id: 'contact', label: 'Contact' }
];

// Toggle mobile menu
const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value;
  
  // Animate menu opening/closing
  if (isMenuOpen.value) {
    gsap.to('.nav-menu', {
      left: 0,
      duration: 0.4,
      ease: 'power2.out'
    });
    
    // Stagger animate menu items
    gsap.from('.nav-item', {
      opacity: 0,
      x: -20,
      stagger: 0.05,
      duration: 0.4,
      delay: 0.2,
      ease: 'power2.out'
    });
  } else {
    gsap.to('.nav-menu', {
      left: '-100%',
      duration: 0.3,
      ease: 'power2.in'
    });
  }
};

// Check scroll position
const checkScroll = () => {
  isScrolled.value = window.scrollY > 50;
  
  // Update active section based on scroll position
  const sections = document.querySelectorAll('section[id]');
  const scrollPosition = window.scrollY + 100; // Offset for navbar height
  
  sections.forEach(section => {
    const sectionTop = section.offsetTop;
    const sectionHeight = section.offsetHeight;
    const sectionId = section.getAttribute('id');
    
    if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
      activeSection.value = sectionId;
    }
  });
};

// Computed property to check if a nav item is active
const isActive = (id) => {
  return activeSection.value === id;
};

// Smooth scroll to section
const scrollToSection = (id) => {
  const element = document.getElementById(id);
  if (element) {
    window.scrollTo({
      top: element.offsetTop - 80, // Adjust for navbar height
      behavior: 'smooth'
    });
  }
  
  // Close mobile menu if open
  if (isMenuOpen.value) {
    isMenuOpen.value = false;
    gsap.to('.nav-menu', {
      left: '-100%',
      duration: 0.3,
      ease: 'power2.in'
    });
  }
};

onMounted(() => {
  window.addEventListener('scroll', checkScroll);
  checkScroll(); // Initial check
  
  // Create a master timeline for navbar entrance
  const navTl = gsap.timeline({
    defaults: {
      ease: "power4.out",
      duration: 1.2
    }
  });
  
  // Initial reveal animation for navbar background with a subtle sweep effect
  navTl.fromTo(navbarRef.value,
    {
      clipPath: "polygon(0 0, 0 0, 0 100%, 0% 100%)",
      opacity: 0
    },
    {
      clipPath: "polygon(0 0, 100% 0, 100% 100%, 0% 100%)",
      opacity: 1,
      duration: 1.4,
      ease: "power3.inOut"
    }
  );
  
  // Logo animation with a sophisticated bounce
  navTl.fromTo(logoRef.value,
    {
      x: -50,
      opacity: 0,
      scale: 0.8,
      filter: "blur(10px)"
    },
    {
      x: 0,
      opacity: 1,
      scale: 1,
      filter: "blur(0px)",
      duration: 1,
      ease: "elastic.out(1, 0.3)"
    },
    "-=1.2"
  );
  
  // Staggered navbar items with a wave effect
  navTl.fromTo(navItems.value,
    {
      y: -40,
      opacity: 0,
      filter: "blur(5px)"
    },
    {
      y: 0,
      opacity: 1,
      filter: "blur(0px)",
      stagger: 0.08,
      duration: 0.8,
      ease: "back.out(1.7)"
    },
    "-=0.8"
  );
  
  // CTA button with a dramatic reveal
  navTl.fromTo('.nav-cta',
    {
      scale: 0,
      opacity: 0,
      rotation: -5
    },
    {
      scale: 1,
      opacity: 1,
      rotation: 0,
      duration: 0.7,
      ease: "elastic.out(1, 0.3)"
    },
    "-=0.5"
  );
  
  // Add subtle pulse animation to CTA button
  gsap.to('.nav-cta', {
    boxShadow: "0 8px 25px rgba(71, 47, 29, 0.25)",
    yoyo: true,
    repeat: -1,
    duration: 2,
    ease: "sine.inOut"
  });
});

onUnmounted(() => {
  window.removeEventListener('scroll', checkScroll);
});
</script>

<template>
  <header :class="['navbar', { 'scrolled': isScrolled }]" ref="navbarRef">
    <div class="container navbar-container">
      <!-- Left side: Company logo with icon -->
      <div class="navbar-left">
        <div class="logo-section" ref="logoRef">
          <a href="#" class="logo-link" @click.prevent="scrollToSection('home')">
            <div class="logo-icon">
              <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8 6C8 4.89543 8.89543 4 10 4H22C23.1046 4 24 4.89543 24 6V8H26C27.1046 8 28 8.89543 28 10V22C28 23.1046 27.1046 24 26 24H24V26C24 27.1046 23.1046 28 22 28H10C8.89543 28 8 27.1046 8 26V6Z" fill="currentColor"/>
                <path d="M12 12H20V16H12V12Z" fill="white"/>
                <path d="M12 18H20V20H12V18Z" fill="white"/>
              </svg>
            </div>
            <span class="logo-text">CLEAR CHOICE CLEANERS</span>
          </a>
        </div>
      </div>

      <!-- Navigation items on the right -->
      <nav :class="['nav-menu', { 'active': isMenuOpen }]">
        <ul class="nav-list">
          <li class="nav-item" ref="navItems">
            <a href="#" @click.prevent="scrollToSection('about')">
              About/Why Us?
            </a>
          </li>
          <li class="nav-item" ref="navItems">
            <a href="#" @click.prevent="scrollToSection('services')">
              Service/Pricing
            </a>
          </li>
          <li class="nav-item" ref="navItems">
            <a href="#" @click.prevent="scrollToSection('contact')">
              Build a Package
            </a>
          </li>
        </ul>
      </nav>
      
      <div class="hamburger" @click="toggleMenu">
        <span :class="['bar', { 'active': isMenuOpen }]"></span>
        <span :class="['bar', { 'active': isMenuOpen }]"></span>
        <span :class="['bar', { 'active': isMenuOpen }]"></span>
      </div>
    </div>
  </header>
</template>

<style scoped>
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  padding: 1rem 0;
  transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  background-color: transparent;
  will-change: transform, opacity;
}

.navbar.scrolled {
  background-color: transparent;
  padding: 0.8rem 0;
}

.navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
}

/* Left side: Company logo */
.navbar-left {
  display: flex;
  align-items: center;
}

.logo-section {
  flex: 0 0 auto;
}

.nav-list {
  margin: 0;
}

.logo-link {
  font-family: 'Bricolage Grotesque', 'Arial', sans-serif;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  will-change: transform, filter;
}

.logo-icon {
  color: var(--color-teal-light);
  transition: color 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.navbar.scrolled .logo-icon {
  color: var(--color-teal);
}

.logo-text {
  font-family: 'Bricolage Grotesque', 'Arial', sans-serif;
  font-size: 1.3rem;
  font-weight: 700;
  color: #FFFFFF !important;
  white-space: nowrap;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: color 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

/* Change logo text color when navbar is scrolled */
.navbar.scrolled .logo-text {
  color: var(--color-teal) !important;
}

.tagline-word {
  color: var(--color-secondary);
  font-style: italic;
}

.logo-link:hover {
  transform: scale(1.05);
}



.logo-accent {
  color: var(--color-secondary);
  position: relative;
  font-weight: 700;
}

.logo-text::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--primary-dark);
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.logo-link:hover .logo-text::after {
  transform: scaleX(1);
  transform-origin: left;
}

/* Navigation on right */
.nav-menu {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex: 0 0 auto;
}

.nav-list {
  display: flex;
  list-style: none;
  gap: 2rem;
  justify-content: flex-end;
}

.nav-item {
  position: relative;
}

.nav-item a {
  color: #FFFFFF !important;
  font-family: 'Bricolage Grotesque', 'Arial', sans-serif;
  font-weight: 500;
  position: relative;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  padding: 0.5rem 0;
  display: inline-block;
  letter-spacing: 0.02em;
  will-change: transform, color;
  text-decoration: none;
}

/* Change text color when navbar is scrolled */
.navbar.scrolled .nav-item a {
  color: var(--color-teal) !important;
}

.nav-item a:hover {
  transform: translateY(-2px);
}

/* Remove scrolled state since navbar is always white */

.nav-indicator {
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--color-teal-light);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  border-radius: 2px;
  opacity: 0;
  transform-origin: left;
  box-shadow: 0 2px 8px rgba(8, 145, 178, 0.4);
}

.nav-item:hover .nav-indicator {
  width: 100%;
  opacity: 1;
}

.nav-item.active .nav-indicator {
  width: 100%;
  opacity: 1;
  height: 3px;
}

.nav-item:hover .nav-indicator,
.nav-item.active .nav-indicator {
  width: 100%;
}

.nav-item.active a {
  font-weight: 600;
}



.hamburger {
  display: none;
  cursor: pointer;
  z-index: 100;
}

.bar {
  display: block;
  width: 25px;
  height: 3px;
  margin: 5px auto;
  transition: all 0.5s cubic-bezier(0.68, -0.6, 0.32, 1.6);
  background-color: var(--primary-dark);
  border-radius: 3px;
  will-change: transform, opacity;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.hamburger:hover .bar {
  transform: scaleX(0.8);
}

.hamburger:hover .bar:nth-child(2) {
  transform: scaleX(0.6);
}

@media (max-width: 768px) {
  .navbar-container {
    justify-content: space-between;
  }

  .navbar-left {
    gap: 1rem;
  }

  .hamburger {
    display: block;
  }

  .nav-menu {
    position: fixed;
    left: -100%;
    top: 0;
    flex-direction: column;
    background-color: rgba(255, 255, 255, 0.95);
    width: 100%;
    height: 100vh;
    text-align: center;
    transition: 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    padding: 6rem 0;
    z-index: 90;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    clip-path: circle(0% at 100% 0);
    will-change: clip-path, left;
  }

  .nav-menu.active {
    left: 0;
    clip-path: circle(150% at 100% 0);
  }


  
  
  .nav-list {
    flex-direction: column;
    gap: 1rem;
  }
  
  .nav-item {
    margin: 1rem 0;
  }
  
  .nav-item a {
    font-size: 1.2rem;
    color: var(--text-dark) !important;
    text-shadow: none !important;
  }
  
  .bar.active:nth-child(1) {
    transform: translateY(8px) rotate(45deg);
  }
  
  .bar.active:nth-child(2) {
    opacity: 0;
    transform: translateX(-20px);
  }
  
  .bar.active:nth-child(3) {
    transform: translateY(-8px) rotate(-45deg);
  }
  
  .nav-cta {
    display: none;
  }
}
</style>